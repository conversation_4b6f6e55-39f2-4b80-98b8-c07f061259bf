syntax = "v1"

info(
	title: "权限配置服务"
	desc: "权限配置相关接口"
	author: "system"
)

type (
	DispositionInfo {
		Id        int64  `json:"id"`
		OrderCode string `json:"orderCode"`
		Vid       int64  `json:"vid"`
		Remark    string `json:"remark"`
		Status    int64  `json:"status"`
	}
	
	DispositionDetailInfo {
		Id            int64  `json:"id"`
		OrderCode     string `json:"orderCode"`
		Vid           int64  `json:"vid"`
		ResourceTypes string `json:"resourceTypes"`
		CpId          int64  `json:"cpId"`
		CpName        string `json:"cpName,optional"`
		BillingType   int64  `json:"billingType"`
		EndTime       int64  `json:"endTime"`
		Remark        string `json:"remark"`
		Status        int64  `json:"status"`
	}

	ListDispositionByVidReq {
		Vid      int64 `json:"vid"`
		Page     int64 `json:"page,default=1"`
		PageSize int64 `json:"pageSize,default=10"`
	}

	ListDispositionResp {
		Total int64             `json:"total"`
		List  []DispositionInfo `json:"list"`
	}
	
	GetValidDispositionDetailReq {
		Vid int64 `json:"vid"`
	}
	
	GetValidDispositionDetailResp {
		List []DispositionDetailInfo `json:"list"`
	}
)

@server(
	middleware: Auth
)
service disposition-api {
	@handler listDispositionByVid
	post /api/disposition/list (ListDispositionByVidReq) returns (ListDispositionResp)
	
	@handler getValidDispositionDetail
	post /api/disposition/valid (GetValidDispositionDetailReq) returns (GetValidDispositionDetailResp)
}
