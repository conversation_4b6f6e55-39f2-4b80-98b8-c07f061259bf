syntax = "v1"

info(
	title: "版权方服务"
	desc: "版权方相关接口"
	author: "system"
)

type (
	CpInfo {
		Id      int64  `json:"id"`
		Name    string `json:"name"`
		Remark  string `json:"remark"`
		Status  int64  `json:"status"`
		EndTime int64  `json:"endTime"`
	}

	ListCpReq {
		Page     int64 `form:"page,default=1"`
		PageSize int64 `form:"pageSize,default=10"`
	}

	ListCpResp {
		Total int64    `json:"total"`
		List  []CpInfo `json:"list"`
	}

	GetCpReq {
		Id int64 `form:"id"`
	}

	GetCpResp {
		Cp CpInfo `json:"cp"`
	}
)

@server(
	middleware: Auth
)
service cp-api {
	@handler listCps
	get /api/cps (ListCpReq) returns (ListCpResp)
	
	@handler getCp
	get /api/cp (GetCpReq) returns (GetCpResp)
}
