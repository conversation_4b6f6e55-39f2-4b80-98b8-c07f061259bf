syntax = "v1"

info(
	title: "热歌服务"
	desc: "热歌榜单相关接口"
	author: "system"
)

type (
	HotTypeInfo {
		Id      int64  `json:"id"`
		HotType int64  `json:"hotType"`
		Name    string `json:"name"`
		Status  int64  `json:"status"`
	}
	
	HotSongInfo {
		Id         int64  `json:"id"`
		HotType    int64  `json:"hotType"`
		SongCode   int64  `json:"songCode"`
		Vid        int64  `json:"vid"`
		Num        int64  `json:"num"`
		SongName   string `json:"songName"`
		SongSinger string `json:"songSinger"`
	}

	ListHotTypesReq {
	}

	ListHotTypesResp {
		List []HotTypeInfo `json:"list"`
	}

	ListHotSongsReq {
		HotType  int64 `form:"hotType"`
		Vid      int64 `form:"vid,default=0"`
		Page     int64 `form:"page,default=1"`
		PageSize int64 `form:"pageSize,default=10"`
	}

	ListHotSongsResp {
		Total int64         `json:"total"`
		List  []HotSongInfo `json:"list"`
	}
)

@server(
	middleware: Auth
)
service hot-api {
	@handler listHotTypes
	get /api/hot/types (ListHotTypesReq) returns (ListHotTypesResp)
	
	@handler listHotSongs
	get /api/hot/songs (ListHotSongsReq) returns (ListHotSongsResp)
}
