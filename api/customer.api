syntax = "v1"

info(
	title: "客户服务"
	desc: "客户相关接口"
	author: "system"
)

type (
	CustomerInfo {
		Id      int64  `json:"id"`
		Name    string `json:"name"`
		Vid     int64  `json:"vid"`
		Cid     int64  `json:"cid"`
		Ip      string `json:"ip"`
		Appid   string `json:"appid"`
		Remark  string `json:"remark"`
		Status  int64  `json:"status"`
		EndTime int64  `json:"endTime"`
	}

	ListCustomerReq {
		Page     int64 `form:"page,default=1"`
		PageSize int64 `form:"pageSize,default=10"`
	}

	ListCustomerResp {
		Total int64          `json:"total"`
		List  []CustomerInfo `json:"list"`
	}

	GetCustomerReq {
		Id  int64 `form:"id,optional"`
		Vid int64 `form:"vid,optional"`
	}

	GetCustomerResp {
		Customer CustomerInfo `json:"customer"`
	}
)

@server(
	middleware: Auth
)
service customer-api {
	@handler listCustomers
	get /api/customers (ListCustomerReq) returns (ListCustomerResp)
	
	@handler getCustomer
	get /api/customer (GetCustomerReq) returns (GetCustomerResp)
}
