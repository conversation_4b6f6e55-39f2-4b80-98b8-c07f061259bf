syntax = "v1"

info(
	title: "认证服务"
	desc: "用户认证相关接口"
	author: "system"
)

type (
	// 注册请求
	RegisterReq {
		Username string `json:"username"`
		Password string `json:"password"`
		Nickname string `json:"nickname,optional"`
		Email    string `json:"email,optional"`
		Phone    string `json:"phone,optional"`
	}

	// 注册响应
	RegisterResp {
		UserId   int64  `json:"userId"`
		UserUuid string `json:"userUuid"`
		Username string `json:"username"`
		Success  bool   `json:"success"`
		Message  string `json:"message,optional"`
	}
	
	// 登录请求
	LoginReq {
		Username string `json:"username"`
		Password string `json:"password"`
	}

	// 登录响应
	LoginResp {
		AccessToken  string `json:"accessToken"`
		AccessExpire int64  `json:"accessExpire"`
		RefreshAfter int64  `json:"refreshAfter"`
		UserId       int64  `json:"userId"`
		UserUuid     string `json:"userUuid"`
		Username     string `json:"username"`
	}
	
	// 登出请求
	LogoutReq {
	}

	// 登出响应
	LogoutResp {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}
	
	// 刷新Token请求
	RefreshTokenReq {
		RefreshToken string `json:"refreshToken"`
	}

	// 刷新Token响应
	RefreshTokenResp {
		AccessToken  string `json:"accessToken"`
		AccessExpire int64  `json:"accessExpire"`
		RefreshAfter int64  `json:"refreshAfter"`
	}
	
	// 用户信息请求
	UserInfoReq {
	}

	// 用户信息响应
	UserInfoResp {
		UserId      int64  `json:"userId"`
		UserUuid    string `json:"userUuid"`
		Username    string `json:"username"`
		Nickname    string `json:"nickname"`
		Email       string `json:"email,optional"`
		Phone       string `json:"phone,optional"`
		Role        int64  `json:"role"`
		Permissions string `json:"permissions"`
		Status      int64  `json:"status"`
		LastLogin   int64  `json:"lastLogin,optional"`
	}
	
	// 修改密码请求
	ChangePasswordReq {
		OldPassword string `json:"oldPassword"`
		NewPassword string `json:"newPassword"`
	}

	// 修改密码响应
	ChangePasswordResp {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}
	
	// 重置密码请求
	ResetPasswordReq {
		Username string `json:"username"`
		Email    string `json:"email,optional"`
		Phone    string `json:"phone,optional"`
		Code     string `json:"code"`
		Password string `json:"newPassword"`
	}

	// 重置密码响应
	ResetPasswordResp {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}
	
	// 查询用户列表请求
	ListUserReq {
		Username string `form:"username,optional"`
		Nickname string `form:"nickname,optional"`
		Role     int64  `form:"role,optional"`
		Page     int64  `form:"page,default=1"`
		PageSize int64  `form:"pageSize,default=10"`
	}

	// 查询用户列表响应
	ListUserResp {
		Total int64          `json:"total"`
		List  []UserInfoItem `json:"list"`
	}

	// 用户列表项
	UserInfoItem {
		UserId     int64  `json:"userId"`
		UserUuid   string `json:"userUuid"`
		Username   string `json:"username"`
		Nickname   string `json:"nickname"`
		Email      string `json:"email,optional"`
		Phone      string `json:"phone,optional"`
		Role       int64  `json:"role"`
		Status     int64  `json:"status"`
		LastLogin  int64  `json:"lastLogin,optional"`
		CreateTime string `json:"createTime"`
		UpdateTime string `json:"updateTime"`
	}
)

// 无需认证的接口
service auth-api {
	@handler register
	post /api/auth/register (RegisterReq) returns (RegisterResp)
	
	@handler login
	post /api/auth/login (LoginReq) returns (LoginResp)
	
	@handler refreshToken
	post /api/auth/refresh (RefreshTokenReq) returns (RefreshTokenResp)
}

// 需要认证的接口
@server(
	middleware: Auth
)
service auth-api {
	@handler logout
	post /api/auth/logout (LogoutReq) returns (LogoutResp)
	
	@handler userInfo
	get /api/auth/user/info (UserInfoReq) returns (UserInfoResp)
	
	@handler changePassword
	post /api/auth/change-password (ChangePasswordReq) returns (ChangePasswordResp)
	
	@handler listUser
	get /api/users (ListUserReq) returns (ListUserResp)
}
