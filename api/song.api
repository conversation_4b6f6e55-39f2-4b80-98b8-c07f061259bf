syntax = "v1"

info(
	title: "歌曲服务"
	desc: "歌曲相关接口，提供歌曲查询、修改等功能"
	author: "system"
	version: "1.0.0"
)

type (
	SongInfo {
		Id                int64  `json:"id"`
		ImportId          int64  `json:"importId"`
		Type              int64  `json:"type"`
		VendorId          int64  `json:"vendorId"`
		VendorName        string `json:"vendorName,optional"`
		SongCode          int64  `json:"songCode"`
		Name              string `json:"name"`
		Singer            string `json:"singer"`
		VendorSongId      string `json:"vendorSongId"`
		VendorReleaseTime string `json:"vendorReleaseTime"`
		SongPath          string `json:"songPath"`
		DrmPath           string `json:"drmPath"`
		LicenseKey        string `json:"licenseKey"`
		PosterPath        string `json:"posterPath"`
		LyricPath         string `json:"lyricPath"`
		LyricType         string `json:"lyricType"`
		Pitchs            string `json:"pitchs"`
		HighPart          string `json:"highPart"`
		HighPartType      int64  `json:"highPartType"`
		QualityLevel      string `json:"qualityLevel"`
		TagIds            string `json:"tagIds"`
		Duration          int64  `json:"duration"`
		CloseTime         int64  `json:"closeTime"`
		HotNum            int64  `json:"hotNum"`
		Status            int64  `json:"status"`
		CreateTime        string `json:"createTime"`
	}

	ListSongReq {
		Page     int64  `json:"page,default=1"`
		PageSize int64  `json:"pageSize,default=10"`
		Name     string `json:"name"`
		Singer   string `json:"singer"`
		VendorId int64  `json:"vendorId"`
		SongCode int64  `json:"songCode"`
	}

	ListSongResp {
		Total   int64             `json:"total"`
		List    []SongInfo        `json:"list"`
		Columns []SongDisplayColumn `json:"columns,optional"` // 用户设置的展示列
	}

	GetSongReq {
		Id       int64 `json:"id,optional"`
		SongCode int64 `json:"songCode,optional"`
	}

	GetSongResp {
		Song SongInfo `json:"song"`
	}

	// 歌曲可选展示列
	SongDisplayColumn {
		Key      string `json:"key"`       // 字段名称
		Title    string `json:"title"`     // 显示标题
		Visible  bool   `json:"visible"`   // 是否显示
		Sortable bool   `json:"sortable"`  // 是否可排序
		Width    int    `json:"width"`     // 宽度（像素）
		Fixed    bool   `json:"fixed"`     // 是否固定列
	}

	// 设置歌曲展示列请求
	SetSongDisplayColumnsReq {
		Columns []SongDisplayColumn `json:"columns"`
	}

	// 设置歌曲展示列响应
	SetSongDisplayColumnsResp {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 获取歌曲展示列请求
	GetSongDisplayColumnsReq {
	}

	// 获取歌曲展示列响应
	GetSongDisplayColumnsResp {
		Columns []SongDisplayColumn `json:"columns"`
	}

	// 更新歌曲请求
	UpdateSongReq {
		Id                int64  `json:"id"`
		ImportId          int64  `json:"importId"`
		Type              int64  `json:"type"`
		VendorId          int64  `json:"vendorId"`
		SongCode          int64  `json:"songCode"`
		Name              string `json:"name"`
		Singer            string `json:"singer"`
		VendorSongId      string `json:"vendorSongId"`
		VendorReleaseTime string `json:"vendorReleaseTime"`
		SongPath          string `json:"songPath"`
		DrmPath           string `json:"drmPath"`
		LicenseKey        string `json:"licenseKey"`
		PosterPath        string `json:"posterPath"`
		LyricPath         string `json:"lyricPath"`
		LyricType         string `json:"lyricType"`
		PitchType         int64  `json:"pitchType"`
		Pitchs            string `json:"pitchs"`
		HighPart          string `json:"highPart"`
		HighPartType      int64  `json:"highPartType"`
		QualityLevel      string `json:"qualityLevel"`
		TagIds            string `json:"tagIds"`
		Duration          int64  `json:"duration"`
		HotNum            int64  `json:"hotNum"`
		Status            int64  `json:"status"`
	}

	// 更新歌曲响应
	UpdateSongResp {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 查询歌曲操作日志请求
	ListSongOperationLogReq {
		SongId   int64 `form:"songId"`
		Page     int64 `form:"page,default=1"`
		PageSize int64 `form:"pageSize,default=10"`
	}

	// 操作日志信息
	OperationLogInfo {
		Id         int64     `json:"id"`
		UserUuid   string    `json:"userUuid"`
		Username   string    `json:"username"`
		Module     string    `json:"module"`
		Operation  string    `json:"operation"`
		EntityId   int64     `json:"entityId"`
		EntityName string    `json:"entityName"`
		OldValue   string    `json:"oldValue,optional"`
		NewValue   string    `json:"newValue,optional"`
		Ip         string    `json:"ip"`
		UserAgent  string    `json:"userAgent"`
		CreateTime string    `json:"createTime"`
	}

	// 查询歌曲操作日志响应
	ListSongOperationLogResp {
		Total int64              `json:"total"`
		List  []OperationLogInfo `json:"list"`
	}
)

@server(
	middleware: Auth
)
service song-api {
	@handler listSongs
	post /api/songs (ListSongReq) returns (ListSongResp)
	
	@handler getSong
	get /api/song (GetSongReq) returns (GetSongResp)

	@handler setSongDisplayColumns
	post /api/song/display-columns (SetSongDisplayColumnsReq) returns (SetSongDisplayColumnsResp)

	@handler getSongDisplayColumns
	get /api/song/display-columns (GetSongDisplayColumnsReq) returns (GetSongDisplayColumnsResp)

	@handler updateSong
	post /api/song/update (UpdateSongReq) returns (UpdateSongResp)

	@handler listSongOperationLog
	get /api/song/operation-logs (ListSongOperationLogReq) returns (ListSongOperationLogResp)
}
