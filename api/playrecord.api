syntax = "v1"

info(
	title: "播放记录服务"
	desc: "歌曲播放记录相关接口"
	author: "system"
)

type (
	// 播放记录信息
	PlayRecordInfo {
		Id           int64     `json:"id"`
		Lts          int64     `json:"lts"`
		Vid          int64     `json:"vid"`
		UniqueId     string    `json:"uniqueId"`
		Cpid         int64     `json:"cpid"`
		SongCode     int64     `json:"songCode"`
		SongName     string    `json:"songName,optional"`
		SongSinger   string    `json:"songSinger,optional"`
		SongDuration int       `json:"songDuration"`
		PlayDuration int       `json:"playDuration"`
		StartTime    int64     `json:"startTime"`
		EndTime      int64     `json:"endTime"`
		PlaybackPos  int       `json:"playbackPos"`
		PlayNum      int       `json:"playNum"`
		SceneType    int       `json:"sceneType"`
		PlayType     int       `json:"playType"`
		FreeType     int       `json:"freeType"`
		RecordType   int       `json:"recordType"`
		UseType      int       `json:"useType"`
		PushStatus   int       `json:"pushStatus"`
		PushTime     int64     `json:"pushTime"`
		CreateTime   string    `json:"createTime"`
	}

	// 添加播放记录请求
	CreatePlayRecordReq {
		Lts          int64  `json:"lts"`
		Vid          int64  `json:"vid"`
		UniqueId     string `json:"uniqueId"`
		RequestIp    string `json:"requestIp,optional"`
		Cpid         int64  `json:"cpid"`
		SongCode     int64  `json:"songCode"`
		SongDuration int    `json:"songDuration"`
		PlayDuration int    `json:"playDuration"`
		StartTime    int64  `json:"startTime"`
		EndTime      int64  `json:"endTime"`
		PlaybackPos  int    `json:"playbackPos"`
		PlayNum      int    `json:"playNum"`
		SceneType    int    `json:"sceneType"`
		PlayType     int    `json:"playType"`
		FreeType     int    `json:"freeType"`
		RecordType   int    `json:"recordType"`
		UseType      int    `json:"useType"`
		Uid          string `json:"uid,optional"`
		ChannelId    string `json:"channelId,optional"`
	}

	// 添加播放记录响应
	CreatePlayRecordResp {
		Id      int64  `json:"id"`
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 查询播放记录请求
	ListPlayRecordReq {
		Vid       int64  `form:"vid,optional"`
		SongCode  int64  `form:"songCode,optional"`
		StartDate string `form:"startDate,optional"`
		EndDate   string `form:"endDate,optional"`
		Page      int64  `form:"page,default=1"`
		PageSize  int64  `form:"pageSize,default=10"`
	}

	// 查询播放记录响应
	ListPlayRecordResp {
		Total int64            `json:"total"`
		List  []PlayRecordInfo `json:"list"`
	}

	// 播放统计请求
	PlayRecordStatsReq {
		StartDate string `form:"startDate"`
		EndDate   string `form:"endDate"`
		Vid       int64  `form:"vid,optional"`
		Cpid      int64  `form:"cpid,optional"`
	}

	// 播放统计数据项
	PlayRecordStatsItem {
		Date       string `json:"date"`
		PlayCount  int64  `json:"playCount"`
		PlayTime   int64  `json:"playTime"`
		SongCount  int64  `json:"songCount"`
	}

	// 播放统计响应
	PlayRecordStatsResp {
		Total     PlayRecordStatsItem    `json:"total"`
		DailyList []PlayRecordStatsItem `json:"dailyList"`
	}

	// 导出播放记录统计请求
	ExportPlayRecordStatsReq {
		StartMonth string `form:"startMonth"`  // 开始月份，格式: YYYY-MM
		EndMonth   string `form:"endMonth"`    // 结束月份，格式: YYYY-MM
	}

	// 导出播放记录统计响应
	ExportPlayRecordStatsResp {
		Success      bool   `json:"success"`      // 是否成功
		Message      string `json:"message"`      // 消息
		FileName     string `json:"fileName"`     // 文件名
		FileSize     int64  `json:"fileSize"`     // 文件大小(字节)
		DownloadUrl  string `json:"downloadUrl"`  // 下载URL
		RecordCount  int    `json:"recordCount"`  // 记录数量
	}
)

// 无需认证的接口
service playrecord-api {
	@handler createPlayRecord
	post /api/playrecord (CreatePlayRecordReq) returns (CreatePlayRecordResp)
}

// 需要认证的接口
@server(
	middleware: Auth
)
service playrecord-api {
	@handler listPlayRecord
	get /api/playrecords (ListPlayRecordReq) returns (ListPlayRecordResp)

	@handler playRecordStats
	get /api/playrecord/stats (PlayRecordStatsReq) returns (PlayRecordStatsResp)

	@handler exportPlayRecordStats
	get /api/playrecord/export_stats (ExportPlayRecordStatsReq) returns (ExportPlayRecordStatsResp)
}
