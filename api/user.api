syntax = "v1"

info(
	title: "用户服务"
	desc: "用户相关接口"
	author: "system"
)

type (
	UserInfo {
		Id          int64  `json:"id"`
		Uuid        string `json:"uuid"`
		Username    string `json:"username"`
		Nickname    string `json:"nickname"`
		Email       string `json:"email,optional"`
		Phone       string `json:"phone,optional"`
		Role        int64  `json:"role"`
		Permissions string `json:"permissions"`
		Status      int64  `json:"status"`
		CreateTime  string `json:"createTime"`
	}

	ListUserReq {
		Page     int64 `form:"page,default=1"`
		PageSize int64 `form:"pageSize,default=10"`
	}

	ListUserResp {
		Total int64      `json:"total"`
		List  []UserInfo `json:"list"`
	}

	GetUserReq {
		Id   int64  `form:"id,optional"`
		Uuid string `form:"uuid,optional"`
	}

	GetUserResp {
		User UserInfo `json:"user"`
	}
	
	UpdateUserReq {
		Id          int64  `json:"id"`
		Nickname    string `json:"nickname,optional"`
		Email       string `json:"email,optional"`
		Phone       string `json:"phone,optional"`
		Role        int64  `json:"role,optional"`
		Permissions string `json:"permissions,optional"`
		Status      int64  `json:"status,optional"`
	}
	
	UpdateUserResp {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}
	
	DeleteUserReq {
		Id int64 `json:"id"`
	}
	
	DeleteUserResp {
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}
	
	// 登录日志
	LoginLogInfo {
		Id         int64  `json:"id"`
		UserId     int64  `json:"userId"`
		UserUuid   string `json:"userUuid"`
		Username   string `json:"username"`
		Ip         string `json:"ip"`
		Location   string `json:"location,optional"`
		UserAgent  string `json:"userAgent"`
		DeviceType string `json:"deviceType,optional"`
		CreateTime string `json:"createTime"`
	}
	
	// 登录日志请求
	ListLoginLogReq {
		Page     int64 `form:"page,default=1"`
		PageSize int64 `form:"pageSize,default=10"`
	}
	
	// 登录日志响应
	ListLoginLogResp {
		Total int64          `json:"total"`
		List  []LoginLogInfo `json:"list"`
	}
)

@server(
	middleware: Auth
)
service user-api {
	@handler listUsers
	post /api/users (ListUserReq) returns (ListUserResp)
	
	@handler getUser
	get /api/user (GetUserReq) returns (GetUserResp)
	
	@handler updateUser
	post /api/user/update (UpdateUserReq) returns (UpdateUserResp)
	
	@handler deleteUser
	post /api/user/delete (DeleteUserReq) returns (DeleteUserResp)
	
	@handler listLoginLogs
	get /api/user/login-logs (ListLoginLogReq) returns (ListLoginLogResp)
}
