version: '1.0'
name: pipeline-********
displayName: pipeline-********
triggers:
  trigger: auto
  push:
    branches:
      precise:
        - daiyb
stages:
  - name: stage-541153eb
    displayName: 未命名
    strategy: naturally
    trigger: auto
    executor: []
    steps:
      - step: build@docker
        name: build_docker
        displayName: 镜像构建
        type: account
        repository: testhub.szjixun.cn:9043/test/music-server
        username: admin
        password: harbor12345
        tag: ${GITEE_PIPELINE_BUILD_NUMBER}
        dockerfile: ./Dockerfile
        context: ''
        artifacts: []
        isCache: true
        parameter: {}
        notify: []
        strategy:
          retry: '0'
  - name: stage-9f8ccedb
    displayName: 未命名
    strategy: naturally
    trigger: auto
    executor: []
    steps:
      - step: deploy@agent
        name: deploy_agent
        displayName: 主机部署
        hostGroupID:
          ID: test
          hostID:
            - 98718b2a-8277-4f16-9bb5-1092c39eecb8
        deployArtifact:
          - source: artifact
            name: output
            target: ~/gitee_go/deploy
            artifactRepository: default
            artifactName: output
            artifactVersion: latest
        script:
          - '# 功能：部署脚本会在部署主机组的每台机器上执行'
          - mkdir /temp/gitee
          - echo 'Hello Gitee Go!'
        notify: []
        strategy:
          retry: '0'
