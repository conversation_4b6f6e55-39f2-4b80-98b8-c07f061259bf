version: '1.0'
name: pipeline-20250702
displayName: pipeline-20250702
triggers:
  trigger: auto
  push:
    branches:
      precise:
        - dev
stages:
  - name: stage-b2b92e16
    displayName: 未命名
    strategy: naturally
    trigger: auto
    executor:
      - music_admin
      - rsalive
      - github-21021215
    steps:
      - step: build@golang
        name: build_golang
        displayName: Golang 构建
        golangVersion: '1.24'
        commands:
          - '# 默认使用goproxy.cn'
          - export GOPROXY=https://goproxy.cn
          - '# 输入你的构建命令'
          - make build
        artifacts:
          - name: BUILD_ARTIFACT
            path:
              - ./output
        caches:
          - /go/pkg/mod
        notify: []
        strategy:
          retry: '0'
