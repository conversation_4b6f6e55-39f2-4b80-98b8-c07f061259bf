go env -w GOOS=linux
cd ./bin
del music-server-test
cd ..
go build  -ldflags "-s -w" -o ./bin/music-server-test ./main.go
docker rmi music-server-test
docker rmi testhub.szjixun.cn:9043/public/music-server-test
docker build . -f .\DockerfileWindowsTest -t music-server-test
docker tag music-server-test testhub.szjixun.cn:9043/public/music-server-test
docker push testhub.szjixun.cn:9043/public/music-server-test
pause