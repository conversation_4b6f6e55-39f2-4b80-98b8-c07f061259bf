package playrecord

import (
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/middleware"
	entitiesKtvBasic "music/model/entities/ktv_basic"
	"net/http"
	"time"

	"errors"
	"github.com/zeromicro/go-zero/rest/httpx"
	"music/internal/logic/playrecord"
	"music/internal/svc"
	"music/internal/types"
)

func SongPlayRecordListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SumPlayRecordReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}
		l := playrecord.NewSongPlayRecordLogic(r.Context(), svcCtx)
		resp, err := l.List(&req)
		middleware.Response(r.Context(), w, resp, err)
	}
}

func ExportSongRecordDetailHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.SumPlayRecordReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}
		l := playrecord.NewSongPlayRecordLogic(r.Context(), svcCtx)
		ok, err := svcCtx.Redis.Setnx(entitiesKtvBasic.LockKeySongDetailExport, time.Now().String())
		if err != nil {
			logx.Errorf("SongPlayRecordToCK 获取锁失败: %v", err)
			middleware.Response(r.Context(), w, nil, errors.New("导出任务正在执行"))
			return
		}
		if !ok {
			logx.Errorf("SongPlayRecordToCK 获取锁失败: %v", err)
			middleware.Response(r.Context(), w, nil, errors.New("导出任务正在执行，请查看导出列表"))
			return
		}
		_ = svcCtx.Redis.Expire(entitiesKtvBasic.LockKeySongDetailExport, 3600)
		err = l.ExportDetail(&req)
		middleware.Response(r.Context(), w, nil, err)
	}
}
