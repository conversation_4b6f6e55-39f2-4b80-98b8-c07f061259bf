package playrecord

import (
	"github.com/zeromicro/go-zero/rest/httpx"
	"music/internal/middleware"
	"music/internal/types"
	"net/http"

	"music/internal/logic/playrecord"
	"music/internal/svc"
)

func DelUniqueIdHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req *types.DelUniqueIDReq
		if err := httpx.Parse(r, &req); err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}
		l := playrecord.NewListPlayRecordGroupBySongLogic(r.Context(), svcCtx)
		err := l.Del<PERSON>epeat<PERSON>ata(req)
		middleware.Response(r.Context(), w, nil, err)
	}
}
