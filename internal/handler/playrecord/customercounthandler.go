package playrecord

import (
	"fmt"
	"io"
	"music/internal/logic/playrecord"
	"music/internal/middleware"
	"music/internal/svc"
	"net/http"
	"os"
	"path/filepath"
)

func CustomerCountExportHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := playrecord.NewCustomRecordCountLogic(r.Context(), svcCtx)
		// 获取文件，"file" 是前端表单中 input 的 name 属性
		file, handler, err := r.FormFile("file")
		if err != nil {
			fmt.Println("无法获取文件: %w", err.Error())
			return
		}
		defer file.Close()

		// 定义保存路径
		savePath := "./file/temp/" + handler.Filename

		// 确保目录存在
		if err := os.MkdirAll(filepath.Dir(savePath), 0755); err != nil {
			fmt.Printf("无法创建目录: %w", err)
			return
		}

		// 创建本地文件
		dst, err := os.Create(savePath)
		if err != nil {
			fmt.Printf("无法创建目录: %w", err)
			return
		}
		defer dst.Close()

		// 复制文件内容
		if _, err := io.Copy(dst, file); err != nil {
			fmt.Printf("无法保存文件: %w", err)
			return
		}
		data, err := l.CustomRecordExport("./file/temp/客户名称20250909.xlsx")
		if err != nil {
			middleware.Response(r.Context(), w, nil, err)
			return
		}
		middleware.Response(r.Context(), w, data, err)
		return
	}
}
