package service

import (
	"compress/gzip"
	"fmt"
	"github.com/robfig/cron/v3"
	"io"
	"music/internal/client"
	"music/internal/svc"
	"music/model/customer"
	"music/model/song"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/atomic"

	playrecordLogic "music/internal/logic/playrecord"
	"music/model/playrecord"
)

// OssMonitorConfig OSS监控配置
type OssMonitorConfig struct {
	Endpoint        string // OSS终端节点
	AccessKeyID     string // 访问密钥ID
	AccessKeySecret string // 访问密钥密码
	BucketName      string // 存储桶名称
	MonitorFolder   string // 需要监控的文件夹路径
	ProcessedFolder string // 处理完成后的文件夹路径
	FailedFolder    string // 处理失败的文件夹路径
	LocalTempDir    string // 本地临时存储目录
	CronSpec        string // 定时任务执行周期 (cron表达式)
}

// OssMonitorService OSS监控服务
type OssMonitorService struct {
	config          OssMonitorConfig
	aoBaiClient     *oss.Client
	yjxClient       *client.YjxOssClient
	bucket          *oss.Bucket
	playRecordModel playrecord.PlayRecordModel
	songModel       song.SongModel
	customerModel   customer.CustomerModel
	processedFiles  map[string]bool // 已处理的文件缓存
	stopChan        chan struct{}   // 停止信号通道
	wg              sync.WaitGroup  // 等待组
	running         atomic.Bool     // 运行状态
	mutex           sync.Mutex      // 互斥锁
	localExportPath string
}

// NewOssMonitorService 创建新的OSS监控服务
func NewOssMonitorService(config OssMonitorConfig, svc *svc.ServiceContext) (*OssMonitorService, error) {
	// 创建OSS客户端
	aoBaiClient, err := oss.New(config.Endpoint, config.AccessKeyID, config.AccessKeySecret)
	if err != nil {
		return nil, fmt.Errorf("创建OSS客户端失败: %w", err)
	}

	// 获取存储桶
	bucket, err := aoBaiClient.Bucket(config.BucketName)
	if err != nil {
		return nil, fmt.Errorf("获取存储桶失败: %w", err)
	}

	// 创建本地临时目录
	if err := os.MkdirAll(config.LocalTempDir, 0755); err != nil {
		return nil, fmt.Errorf("创建本地临时目录失败: %w", err)
	}

	yjxClient, err := client.NewYjxOssClient(&svc.Config)
	if err != nil {
		return nil, fmt.Errorf("创建OSS客户端失败: %w", err)
	}

	return &OssMonitorService{
		config:          config,
		aoBaiClient:     aoBaiClient,
		yjxClient:       yjxClient,
		bucket:          bucket,
		playRecordModel: svc.PlayRecordModel,
		processedFiles:  make(map[string]bool),
		stopChan:        make(chan struct{}),
		songModel:       svc.SongModel,
		customerModel:   svc.CustomerModel,
		localExportPath: svc.Config.File.ExportPath,
	}, nil
}

// StartTicker 启动监控服务
func (s *OssMonitorService) StartTicker() error {
	// 如果服务已经在运行，直接返回
	if !s.running.CompareAndSwap(false, true) {
		return fmt.Errorf("OSS监控服务已在运行")
	}

	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		defer s.running.Store(false)

		// 创建定时器
		ticker := time.NewTicker(parseCronDuration(s.config.CronSpec))
		defer ticker.Stop()

		// 延迟60秒后执行第一次检查
		//time.Sleep(60 * time.Second)

		// 首次执行
		//s.safeCheckNewFiles()

		// 定时执行
		for {
			select {
			case <-ticker.C:
				s.safeCheckNewFiles()
			case <-s.stopChan:
				logx.Info("OSS监控服务收到停止信号")
				return
			}
		}
	}()

	logx.Infof("OSS监控服务已启动，监控目录: %s，执行周期: %s", s.config.MonitorFolder, s.config.CronSpec)
	return nil
}

// StartCron 启动监控服务
func (s *OssMonitorService) StartCron() error {
	// 如果服务已经在运行，直接返回
	if !s.running.CompareAndSwap(false, true) {
		return fmt.Errorf("OSS监控服务已在运行")
	}

	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		defer s.running.Store(false)

		// 使用cron库创建定时任务
		c := cron.New()
		_, err := c.AddFunc(s.config.CronSpec, func() {
			s.safeCheckNewFiles()
		})

		if err != nil {
			logx.Errorf("添加cron任务失败: %v", err)
			return
		}

		c.Start()
		defer c.Stop()

		// 首次执行
		s.safeCheckNewFiles()

		// 等待停止信号
		<-s.stopChan
		logx.Info("OSS监控服务收到停止信号")
	}()

	logx.Infof("OSS监控服务 定时任务 已启动，监控目录: %s，执行周期: %s", s.config.MonitorFolder, s.config.CronSpec)
	return nil
}

// Stop 停止监控服务
func (s *OssMonitorService) Stop() {
	if s.running.Load() {
		close(s.stopChan)
		s.wg.Wait()
		logx.Info("OSS监控服务已停止")
	}
}

// safeCheckNewFiles 安全地执行文件检查
func (s *OssMonitorService) safeCheckNewFiles() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if err := s.checkNewFiles(); err != nil {
		logx.Errorf("检查新文件出错: %v", err)
	}
}

// parseCronDuration 解析cron表达式为duration
func parseCronDuration(cronSpec string) time.Duration {
	// 这里简化处理，假设cronSpec格式为 "0 */n * * * *" (每n分钟)
	parts := strings.Split(cronSpec, " ")
	if len(parts) >= 3 {
		if n, err := strconv.Atoi(strings.Trim(parts[1], "*/}")); err == nil {
			return time.Duration(n) * time.Minute
		}
	}
	// 默认5分钟
	return 5 * time.Minute
}

// IsRunning 检查服务是否正在运行
func (s *OssMonitorService) IsRunning() bool {
	return s.running.Load()
}

// checkNewFiles 检查OSS中是否有新文件
func (s *OssMonitorService) checkNewFiles() error {
	logx.Info("开始检查OSS中的新文件...")

	// 获取该目录下的文件列表
	prefix := strings.TrimRight(s.config.MonitorFolder, "/") + "/"
	marker := ""

	logx.Infof("监控目录: %s", prefix)

	logx.Info("开始列出已处理的文件...")
	logx.Infof("已处理的文件: %v", s.processedFiles)

	for {
		logx.Infof("列出OSS对象，前缀: %s, 标记: %s", prefix, marker)

		lsRes, err := s.bucket.ListObjects(oss.Prefix(prefix), oss.Marker(marker))
		if err != nil {
			return fmt.Errorf("列出OSS对象失败: %w", err)
		}

		// 处理当前页的文件
		for _, object := range lsRes.Objects {

			logx.Infof("发现文件: %s", object.Key)

			// 跳过目录本身
			if object.Key == prefix {
				continue
			}

			// 检查文件扩展名
			ext := strings.ToLower(filepath.Ext(object.Key))
			if ext != ".gz" {
				logx.Infof("跳过非CSV文件: %s", object.Key)
				continue
			}

			// 如果文件已经处理过，跳过
			if s.processedFiles[object.Key] {
				logx.Infof("文件已处理过，跳过: %s", object.Key)
				continue
			}

			// 处理新文件
			logx.Infof("发现新文件: %s", object.Key)
			if err := s.processFile(object.Key); err != nil {
				logx.Errorf("处理文件 %s 失败: %v", object.Key, err)
				// 移动到失败目录
				newKey := strings.TrimRight(s.config.FailedFolder, "/") + "/" + filepath.Base(object.Key)
				if err := s.moveFile(object.Key, newKey); err != nil {
					logx.Errorf("移动失败文件到失败目录失败: %v", err)
				}
			} else {
				// 移动到已处理目录
				newKey := strings.TrimRight(s.config.ProcessedFolder, "/") + "/" + filepath.Base(object.Key)
				if err := s.moveFile(object.Key, newKey); err != nil {
					logx.Errorf("移动文件到已处理目录失败: %v", err)
				} else {
					logx.Infof("文件移动到已处理目录成功: %s", newKey)
					// 标记为已处理
					s.processedFiles[object.Key] = true
				}
			}
		}

		// 如果不是完整列表，继续获取下一页
		if lsRes.IsTruncated {
			marker = lsRes.NextMarker
		} else {
			break
		}
	}

	logx.Info("OSS新文件检查完成")
	return nil
}

// processFile 处理OSS中的文件
func (s *OssMonitorService) processFile(objectKey string) error {
	// 下载文件到本地临时目录
	localFilePath := filepath.Join(s.config.LocalTempDir, filepath.Base(objectKey))
	if err := s.downloadFile(objectKey, localFilePath); err != nil {
		return fmt.Errorf("下载文件失败: %w", err)
	}
	defer os.Remove(localFilePath) // 处理完删除临时文件

	// 如果是 gzip 文件，解压并读取
	var inputFile string
	if strings.HasSuffix(localFilePath, ".gz") {
		// 创建解压后的文件路径
		uncompressedPath := strings.TrimSuffix(localFilePath, ".gz")
		if err := uncompressGzipFile(localFilePath, uncompressedPath); err != nil {
			return fmt.Errorf("解压文件失败: %w", err)
		}
		defer os.Remove(uncompressedPath) // 处理完删除解压文件
		inputFile = uncompressedPath
	} else {
		inputFile = localFilePath
	}

	// 设置失败输出路径
	fileExt := filepath.Ext(strings.TrimSuffix(localFilePath, ".gz"))
	baseFilename := filepath.Base(localFilePath[:len(localFilePath)-len(fileExt)-3]) // 移除 .csv.gz
	timestamp := time.Now().Format("20060102150405")
	failedOutputPath := filepath.Join(s.config.LocalTempDir, fmt.Sprintf("%s_%s_failed%s", baseFilename, timestamp, fileExt))

	// 导入数据
	var result *playrecordLogic.ImportResult
	var playRecords []*playrecord.PlayRecord
	result, playRecords, err := playrecordLogic.ImportPlayRecordsFromFileV2(s.playRecordModel, inputFile, failedOutputPath)
	if err != nil {
		return fmt.Errorf("导入数据失败: %w", err)
	}

	go func() {
		// 使用重试机制处理音集协播放记录
		err = s.withRetry("生成音集协播放记录", 3, func() error {
			return playrecordLogic.MakeAssociationPlayRecord(s.yjxClient, s.songModel, s.customerModel, baseFilename, playRecords)
		})
		if err != nil {
			logx.Errorf("生成音集协播放记录最终失败: %v", err)
			return
		}
		logx.Infof("音集协播放记录生成成功，记录数: %d", len(playRecords))
	}()

	// 如果有失败记录，上传失败记录文件到OSS
	if result.FailedCount > 0 {
		failedOssKey := strings.TrimRight(s.config.FailedFolder, "/") + "/" + filepath.Base(failedOutputPath)
		if err := s.uploadFile(failedOutputPath, failedOssKey); err != nil {
			logx.Errorf("上传失败记录文件到OSS失败: %v", err)
		} else {
			logx.Infof("失败记录文件上传成功: %s", failedOssKey)
			// 删除本地失败记录文件
			os.Remove(failedOutputPath)
		}
	}

	logx.Infof("文件 %s 处理完成: 总记录 %d, 成功 %d, 失败 %d",
		objectKey, result.TotalCount, result.SuccessCount, result.FailedCount)

	return nil
}

// downloadFile 从OSS下载文件到本地
func (s *OssMonitorService) downloadFile(objectKey, localPath string) error {
	// 确保本地临时目录存在
	if err := os.MkdirAll(filepath.Dir(localPath), 0777); err != nil {
		return fmt.Errorf("创建本地临时目录失败: %w", err)
	}

	/*	// 创建本地文件
		f, err := os.Create(localPath)
		if err != nil {
			return fmt.Errorf("创建本地文件失败: %w", err)
		}
		defer f.Close()
	*/
	// 获取文件元信息
	meta, err := s.bucket.GetObjectDetailedMeta(objectKey)
	if err != nil {
		return fmt.Errorf("获取文件元信息失败: %w", err)
	}

	fileSize, _ := strconv.ParseInt(meta.Get("Content-Length"), 10, 64)
	logx.Infof("文件大小: %v", fileSize)

	// 创建进度条
	//progressBar := &ProgressListener{
	//	totalSize: fileSize,
	//	objectKey: objectKey,
	//	startTime: time.Now(),
	//	logPrefix: "下载进度",
	//}

	// 下载文件，使用进度监听
	err = s.bucket.DownloadFile(
		objectKey,
		localPath,
		fileSize,
		//1000*1024, // 分片大小，1M
		//oss.Progress(progressBar),
		//oss.Routines(3),
		//oss.Checkpoint(true, ""),
	)
	if err != nil {
		// 清理断点续传文件
		os.Remove(localPath + ".cp")
		return fmt.Errorf("清理断点续传文件失败: %w", err)
	}

	/*// 验证文件大小
	fi, err := f.Stat()
	if err != nil {
		return fmt.Errorf("获取本地文件信息失败: %w", err)
	}

	if fi.Size() != fileSize {
		return fmt.Errorf("文件大小不匹配，期望: %d, 实际: %d", fileSize, fi.Size())
	}*/

	logx.Infof("文件 %s 下载完成，大小: %d 字节", objectKey, fileSize)
	return nil
}

// uploadFile 上传本地文件到OSS
func (s *OssMonitorService) uploadFile(localPath, objectKey string) error {
	// 打开本地文件
	f, err := os.Open(localPath)
	if err != nil {
		return err
	}
	defer f.Close()

	// 上传到OSS
	return s.bucket.PutObject(objectKey, f)
}

// moveFile 在OSS中移动文件
func (s *OssMonitorService) moveFile(sourceKey, destKey string) error {
	// 在OSS中，移动操作是通过复制然后删除原文件实现的
	if _, err := s.bucket.CopyObject(sourceKey, destKey); err != nil {
		return err
	}

	// 删除原文件
	return s.bucket.DeleteObject(sourceKey)
}

// uncompressGzipFile 解压 gzip 文件
func uncompressGzipFile(gzipFile, destFile string) error {
	// 打开压缩文件
	gzipStream, err := os.Open(gzipFile)
	if err != nil {
		return err
	}
	defer gzipStream.Close()

	// 创建 gzip reader
	reader, err := gzip.NewReader(gzipStream)
	if err != nil {
		return err
	}
	defer reader.Close()

	// 创建目标文件
	writer, err := os.Create(destFile)
	if err != nil {
		return err
	}
	defer writer.Close()

	// 复制解压后的内容到目标文件
	_, err = io.Copy(writer, reader)
	return err
}

// ProgressListener 用于显示下载进度
type ProgressListener struct {
	totalSize int64
	objectKey string
	startTime time.Time
	lastLog   time.Time
	logPrefix string
}

func (p *ProgressListener) ProgressChanged(event *oss.ProgressEvent) {
	switch event.EventType {
	case oss.TransferStartedEvent:
		logx.Infof("%s开始: %s", p.logPrefix, p.objectKey)

	case oss.TransferDataEvent:
		now := time.Now()
		// 每5秒记录一次进度
		if now.Sub(p.lastLog) >= 5*time.Second {
			percent := float64(event.ConsumedBytes) * 100.0 / float64(p.totalSize)
			speed := float64(event.ConsumedBytes) / time.Since(p.startTime).Seconds() / 1024 // KB/s
			logx.Infof("%s: %s %.2f%% (%.2f KB/s)",
				p.logPrefix,
				p.objectKey,
				percent,
				speed,
			)
			p.lastLog = now
		}

	case oss.TransferCompletedEvent:
		duration := time.Since(p.startTime)
		speed := float64(p.totalSize) / duration.Seconds() / 1024 // KB/s
		logx.Infof("%s完成: %s, 耗时: %.2fs, 平均速度: %.2f KB/s",
			p.logPrefix,
			p.objectKey,
			duration.Seconds(),
			speed,
		)

	case oss.TransferFailedEvent:
		logx.Errorf("%s失败: %s", p.logPrefix, p.objectKey)
	}
}

// withRetry 通用重试机制，使用指数退避策略
func (s *OssMonitorService) withRetry(operation string, maxRetries int, fn func() error) error {
	var lastErr error

	for attempt := 1; attempt <= maxRetries; attempt++ {
		err := fn()
		if err == nil {
			if attempt > 1 {
				logx.Infof("%s 在第 %d 次尝试后成功", operation, attempt)
			}
			return nil
		}

		lastErr = err
		logx.Errorf("%s 第 %d 次尝试失败: %v", operation, attempt, err)

		// 如果不是最后一次尝试，使用指数退避策略等待
		if attempt < maxRetries {
			// 指数退避：1s, 2s, 4s, 8s...，最大不超过30秒
			waitTime := time.Duration(1<<uint(attempt-1)) * time.Second
			if waitTime > 30*time.Second {
				waitTime = 30 * time.Second
			}
			logx.Infof("%s 将在 %v 后进行第 %d 次重试", operation, waitTime, attempt+1)
			time.Sleep(waitTime)
		}
	}

	return fmt.Errorf("%s 在 %d 次尝试后最终失败: %w", operation, maxRetries, lastErr)
}

// withRetryCustom 自定义重试机制，允许指定重试间隔
func (s *OssMonitorService) withRetryCustom(operation string, maxRetries int, retryInterval time.Duration, fn func() error) error {
	var lastErr error

	for attempt := 1; attempt <= maxRetries; attempt++ {
		err := fn()
		if err == nil {
			if attempt > 1 {
				logx.Infof("%s 在第 %d 次尝试后成功", operation, attempt)
			}
			return nil
		}

		lastErr = err
		logx.Errorf("%s 第 %d 次尝试失败: %v", operation, attempt, err)

		// 如果不是最后一次尝试，等待指定时间
		if attempt < maxRetries {
			logx.Infof("%s 将在 %v 后进行第 %d 次重试", operation, retryInterval, attempt+1)
			time.Sleep(retryInterval)
		}
	}

	return fmt.Errorf("%s 在 %d 次尝试后最终失败: %w", operation, maxRetries, lastErr)
}
