package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/rest"
)

// Config 应用配置结构
type Config struct {
	rest.RestConf
	System struct {
		Mode string `json:"Mode"`
	}
	Auth struct {
		AccessSecret string `json:",default=defaultAccessSecret"`
		AccessExpire int64  `json:",default=86400"`
	}
	MysqlOut struct {
		DataSource string
	}
	MysqlIn struct {
		DataSource string
	}
	CkIn struct {
		DataSource string
	}
	// 使用Redis配置
	Redis redis.RedisConf // Redis配置
	File  struct {
		UploadPath string `json:",default=./file/upload"`
		ExportPath string `json:",default=./file/exports"`
	}
	Oss struct {
		Endpoint        string `json:"Endpoint"`
		AccessKeyID     string `json:"AccessKeyID"`
		AccessKeySecret string `json:"AccessKeySecret"`
		BucketName      string `json:"BucketName"`
		MonitorFolder   string `json:"MonitorFolder"`
		ProcessedFolder string `json:"ProcessedFolder"`
		FailedFolder    string `json:"FailedFolder"`
		CronSpec        string `json:"CronSpec"`
	}
	YjxOss struct {
		EndPoint        string
		AccessKeyId     string
		AccessKeySecret string
		BucketName      string
		PlatformOssPath string
		AnchorOssPath   string
		LicenseOssPath  string
	}
}

// Validate 配置验证方法
func (c *Config) Validate() error {
	// 验证数据库连接字符串
	if c.MysqlOut.DataSource == "" {
		return ErrEmptyDatabaseSource
	}

	if c.MysqlIn.DataSource == "" {
		return ErrEmptyDatabaseSource
	}

	// 验证Redis配置
	if c.Redis.Host == "" {
		logx.Errorf("未配置Redis地址，可能影响系统功能")
	}

	// 验证JWT配置
	if c.Auth.AccessSecret == "defaultAccessSecret" {
		// 默认的secret时应该警告
		logx.Error("Using default JWT access secret, please change it in production environment")
	}

	return nil
}
