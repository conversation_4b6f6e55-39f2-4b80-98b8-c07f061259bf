package user

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/svc"
	"music/internal/types"
)

type AuthInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAuthInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AuthInfoLogic {
	return &AuthInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AuthInfoLogic) UserInfo() (*types.AuthUser, error) {
	authUser := &types.AuthUser{}
	if l.svcCtx.Config.System.Mode == "dev" {
		authUser = &types.AuthUser{
			UserId:   4,
			UserUuid: "565be3dc-b000-41f3-931e-4befe92721e5",
			Username: "超级管理员",
			Role:     3,
		}
	} else {
		authUser = l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	}
	return authUser, nil
}
