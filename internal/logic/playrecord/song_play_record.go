package playrecord

import (
	"context"
	"errors"
	"fmt"
	"music/internal/logic/export"
	"music/internal/middleware"
	"music/internal/svc"
	"music/internal/types"
	entitiesCK "music/model/entities/ck"
	entitiesKtv "music/model/entities/ktv"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type SongPlayRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSongPlayRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SongPlayRecordLogic {
	return &SongPlayRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SongPlayRecordLogic) List(req *types.SumPlayRecordReq) (resp *types.SumPlayRecordResp, err error) {
	resp = new(types.SumPlayRecordResp)
	// 验证用户权限
	if err = l.Check<PERSON>uth(); err != nil {
		return nil, err
	}
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}
	var data []entitiesCK.SongPlayRecordList
	var query *gorm.DB
	var timeDiff string
	if req.TimeZone == "" {
		req.TimeZone = fmt.Sprint(types.UTC)
	}
	if req.TimeZone == fmt.Sprint(types.UTC) {
		timeDiff = "song_play_record.start_time - 28800"
	}
	if req.TimeZone == fmt.Sprint(types.BJ) {
		timeDiff = "song_play_record.start_time"
	}
	selectColumns := "formatDateTime(toDate(" + timeDiff + "), '%Y-%m-%d') as lts_time,SUM(song_play_record.play_num) AS play_num_total,SUM(song_play_record.play_duration) AS play_duration_total,MAX(song_play_record.play_type) AS max_play_type"
	if query, err = l.queryFilter(req, selectColumns); err != nil {
		return
	}
	query.Group("toDate(" + timeDiff + ")")
	if err = query.Count(&resp.Total).Error; err != nil {
		l.Logger.Errorf("SongPlayRecordLogic.count err:%v", err)
		err = errors.New(types.ErrSelect)
		return
	}
	if err = query.Order("lts_time asc").Limit(int(req.PageSize)).
		Offset(int((req.Page - 1) * req.PageSize)).Find(&data).Error; err != nil {
		l.Logger.Errorf("SongPlayRecordLogic.List err:%v", err)
		err = errors.New(types.ErrSelect)
		return
	}
	for _, v := range data {
		resp.List = append(resp.List, types.SumPlayRecord{
			LtsTime:           v.LtsTime,
			PlayNumTotal:      v.PlayNumTotal,
			PlayDurationTotal: v.PlayDurationTotal,
			PlayType:          v.MaxPlayType,
		})
	}
	return
}

func (l *SongPlayRecordLogic) StartEndTime(req *types.SumPlayRecordReq) (startTimeUnix, endTimeUnix int64, err error) {
	startTime := middleware.DefaultMonthToUnixStartTime(middleware.MonthLimit)
	if req.StartDate == "" && req.EndDate == "" && req.Month == "" {
		startTimeUnix = startTime
		endTimeUnix = middleware.NowToUnixStartTime()
	}
	if req.StartDate != "" && req.EndDate != "" {
		startTimeUnix, err = middleware.BeginDateToUnixStartTime(req.StartDate, middleware.MonthLimit)
		if err != nil {
			return
		}
		endTimeUnix, err = middleware.EndDateToUnixStartTime(req.EndDate)
		if err != nil {
			return
		}
	}
	if req.Month != "" {
		startTimeUnix, endTimeUnix, err = middleware.MonthConvertToBeginAndEndToUnixStartTime(req.Month, middleware.MonthLimit)
		if err != nil {
			return
		}
	}
	if req.PlayType == 0 {
		err = errors.New(types.ErrNotPlayType)
		return
	}
	return
}

// 过滤歌曲的条件
func (l *SongPlayRecordLogic) songWhere(req *types.SumPlayRecordReq, query *gorm.DB) (err error) {
	//var songData []*entitiesKtv.Song
	//var songCodes []int64
	// 歌手
	if req.Singer != "" {
		query.Where("song.singer like ?", "%"+req.Singer+"%")
	}
	// 歌词类型
	if len(req.LyricType) > 0 {
		for _, v := range req.LyricType {
			query.Where("hasToken(song.lyric_type,?) > 0", v)
		}
	}
	// 打分类型
	if req.Pitchs != "" {
		//var where string
		patchsData := strings.Split(req.Pitchs, ",")
		for _, v := range patchsData {
			query.Where("hasToken(song.pitchs,?) > 0", v)
		}
	}
	// 副歌类型
	if req.HighPartType != 0 {
		query.Where("song.high_part_type = ?", req.HighPartType)
	}
	// 标签
	if req.TagIDs != "" {
		tagIDsData := strings.Split(req.TagIDs, ",")
		for _, v := range tagIDsData {
			query.Where("hasToken(song.tag_ids,?) > 0", v)
		}
	}
	// 状态
	if req.Status != -1 {
		query.Where("song.status = ?", req.Status)
	}
	// 上架开始时间
	if req.CreateTimeStart != "" && req.CreateTimeEnd != "" {
		var endTime int64
		var endDate string
		endTime, err = middleware.EndDateToUnixStartTime(req.CreateTimeEnd)
		if err != nil {
			return
		}
		endTime = endTime + 86400
		endDate = time.Unix(endTime, 0).Format("2006-01-02")
		query.Where("song.create_time >= ?", req.CreateTimeStart).Where("song.create_time < ?", endDate)
	}
	// 下架开始时间
	if req.CloseTimeStart != "" && req.CloseTimeEnd != "" {
		var beginTime, endTime int64
		beginTime, err = middleware.EndDateToUnixStartTime(req.CloseTimeStart)
		if err != nil {
			return
		}
		endTime, err = middleware.EndDateToUnixStartTime(req.CloseTimeEnd)
		if err != nil {
			return
		}
		endTime = endTime + 86400
		query.Where("song.close_time >= ?").Where("song.close_time < ?", beginTime, endTime)
	}
	if req.VendorSongID != "" {
		query.Where("song.vendor_song_id = ?", req.VendorSongID)
	}
	return
}

// 组装条件
func (l *SongPlayRecordLogic) queryFilter(req *types.SumPlayRecordReq, selectColumns string) (query *gorm.DB, err error) {
	var startTimeUnix int64
	var endTimeUnix int64
	if startTimeUnix, endTimeUnix, err = l.StartEndTime(req); err != nil {
		return
	}
	var customerVids []int64
	if req.CustomerName != "" {
		var customerData []entitiesKtv.Customer
		if err = l.svcCtx.DBKtv.Model(entitiesKtv.Customer{}).Where("trim(name) = ?", req.CustomerName).Find(&customerData).Error; err != nil {
			l.Logger.Errorf("SongPlayRecordLogic.List err:%v", err)
			err = errors.New(types.ErrQuerySongPlayRecord)
			return
		}
		if len(customerData) == 0 {
			return
		}
		for _, v := range customerData {
			customerVids = append(customerVids, v.Vid)
		}
	}
	if req.Vid != 0 {
		customerVids = append(customerVids, req.Vid)
	}
	//var whereSongCodes []int64
	query = l.svcCtx.DBCkIn.Model(&entitiesCK.SongPlayRecord{}).
		Select(selectColumns).
		Joins("left join song on song.song_code = song_play_record.song_code ")
	if err = l.songWhere(req, query); err != nil {
		return
	}
	// 处理歌曲查询
	query.Where("song_play_record.start_time between ? and ?", startTimeUnix, endTimeUnix).Where("song_play_record.play_type = ?", req.PlayType)
	if req.SongName != "" {
		query.Where("song.name like ?", "%"+req.SongName+"%")
	}
	if len(customerVids) > 0 {
		query.Where("song_play_record.vid in (?)", customerVids)
	}
	if req.UseType != 0 {
		query.Where("song_play_record.use_type = ?", req.UseType)
	}
	if req.SceneType != 0 {
		query.Where("song_play_record.scene_type = ?", req.SceneType)
	}
	if req.PlayDuration == 1 {
		query.Where("song_play_record.play_duration <= 50")
	}
	if req.PlayDuration == 2 {
		query.Where("song_play_record.play_duration > 50")
	}
	if req.CpID != 0 {
		query.Where("song_play_record.cp_id = ?", req.CpID)
	}
	return
}

// 导出调用详情
func (l *SongPlayRecordLogic) ExportDetail(req *types.SumPlayRecordReq) (err error) {
	// 验证用户权限
	if err = l.CheckAuth(); err != nil {
		return
	}
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}
	//var data []entitiesCK.SongPlayRecord
	var query *gorm.DB
	var total int64
	//var timeDiff string
	if req.TimeZone == "" {
		req.TimeZone = fmt.Sprint(types.UTC)
	}
	//if req.TimeZone == fmt.Sprint(types.UTC) {
	//	timeDiff = "song_play_record.start_time - 28800"
	//}
	//if req.TimeZone == fmt.Sprint(types.BJ) {
	//	timeDiff = "song_play_record.start_time"
	//}
	// 查询所有的customer数据，按照kv分组
	// FIXME 调用量 调用时长
	selectColumns := "song_play_record.id,song_play_record.unique_id,song.name as song_name,song_play_record.song_code,song_play_record.cp_id," +
		"song_play_record.song_duration,song_play_record.play_duration,song_play_record.start_time,song_play_record.end_time," +
		"song_play_record.playback_pos,song_play_record.play_num,song_play_record.scene_type,song_play_record.record_type," +
		"song_play_record.play_type,song_play_record.free_type," +
		"song_play_record.vid,song_play_record.use_type"
	if query, err = l.queryFilter(req, selectColumns); err != nil {
		return
	}
	if err = query.Count(&total).Error; err != nil {
		l.Logger.Errorf("ExportDetail.count err:%v", err)
		err = errors.New(types.ErrSelect)
		return
	}
	/*if err = query.Order("song_play_record.id asc").Limit(int(req.PageSize)).
		Offset(int((req.Page - 1) * req.PageSize)).Find(&data).Error; err != nil {
		l.Logger.Errorf("ExportDetail.List err:%v", err)
		err = errors.New(types.ErrSelect)
		return
	}*/
	//for _, v := range data {
	//
	//}
	go func() {
		logicExport := export.NewLogicExport(l.ctx, l.svcCtx)
		if err = logicExport.RecordDetailExport(query, total); err != nil {
			l.Logger.Errorf("ExportDetail.List err:%v", err)
			err = errors.New(fmt.Sprintf("导出失败:%v", err.Error()))
			return
		}
	}()
	return
}

func (l *SongPlayRecordLogic) CheckAuth() (err error) {
	return
	//FIXME
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		err = errors.New(types.ErrUserAuthFailed)
		return
	}
	if authUser.Role < middleware.RoleAdmin {
		err = middleware.ErrNoPermission
		return
	}
	return
}
