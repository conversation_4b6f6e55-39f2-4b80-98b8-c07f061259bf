package playrecord

import (
	"archive/zip"
	"context"
	"errors"
	"fmt"
	"io"
	"math"
	"music/internal/middleware"
	"music/internal/types"
	"os"
	"path/filepath"
	"runtime"
	"sort"
	"sync"
	"time"

	"github.com/tealeg/xlsx"
	"github.com/zeromicro/go-zero/core/logx"
)

//	V2
//
// getPlayStats 获取播放统计数据 - 优化版本，支持分批处理
func (l *ExportPlayRecordStatsLogic) getPlayStatsV2(startTime, endTime int64, dates []string, playType int, vid []int64, useType int64, timeZoneType types.TimeZoneType, customerNameMap map[int64]types.ExportCustomerInfo) (map[string]map[int64]*types.DailyPlayStat, error) {
	// 统计结果: 日期 -> VID -> 统计数据
	stats := make(map[string]map[int64]*types.DailyPlayStat)

	// 初始化日期映射
	if dates == nil || len(dates) == 0 {
		// 计算日期范围
		currentTime := time.Unix(startTime, 0)
		endDateTime := time.Unix(endTime, 0)

		// 检查日期范围是否过大
		daysDiff := int(endDateTime.Sub(currentTime).Hours()/24) + 1
		if daysDiff > 90 {
			logx.Debugf("日期范围过大: %d 天，可能导致内存压力", daysDiff)
		}

		// 初始化所有日期的统计数据
		for currentTime.Before(endDateTime) || currentTime.Equal(endDateTime) {
			dateStr := currentTime.Format("2006-01-02")
			stats[dateStr] = make(map[int64]*types.DailyPlayStat)
			currentTime = currentTime.AddDate(0, 0, 1)
		}
	} else {
		for _, date := range dates {
			stats[date] = make(map[int64]*types.DailyPlayStat)
		}
	}

	// 分批处理客户ID
	batchSize := 20 // 每批处理的客户数量
	if len(vid) == 0 {
		// 如果没有指定客户，则返回空结果
		return stats, nil
	}

	// 分批处理
	for i := 0; i < len(vid); i += batchSize {
		end := i + batchSize
		if end > len(vid) {
			end = len(vid)
		}

		batchVids := vid[i:end]
		logx.Infof("处理客户批次 %d/%d, 客户数: %d", i/batchSize+1, (len(vid)+batchSize-1)/batchSize, len(batchVids))

		// 查询该批次客户的播放记录
		records, err := l.svcCtx.PlayRecordModel.FindByPlayTypeGroupByStartTimeDatesForExportV3(
			startTime, endTime, dates, playType, batchVids, useType, timeZoneType)
		if err != nil {
			return nil, fmt.Errorf("批次 %d 查询失败: %w", i/batchSize+1, err)
		}

		// 初始化该批次客户的统计数据
		for dateStr := range stats {
			for _, vid := range batchVids {
				if customerInfo, exists := customerNameMap[vid]; exists {
					stats[dateStr][vid] = &types.DailyPlayStat{
						Date:         dateStr,
						CustomerName: customerInfo.Name,
						Vid:          vid,
						Cid:          customerInfo.Cid,
						PlayCount:    0,
					}
				}
			}
		}

		// 统计播放记录
		for _, record := range records {
			if dateStats, ok := stats[record.LtsTime]; ok {
				if stat, ok := dateStats[record.Vid]; ok {
					stat.PlayCount = record.PlayNumTotal
				}
			}
		}

		// 释放内存
		records = nil
		runtime.GC()
	}

	return stats, nil
}

// exportStatsToExcel 将统计数据导出为Excel - 优化版本，减少内存使用
func (l *ExportPlayRecordStatsLogic) exportStatsToExcelV2(stats map[string]map[int64]*types.DailyPlayStat, filePath string, isClip string) error {
	file := xlsx.NewFile()
	sheet, err := file.AddSheet("data")
	if err != nil {
		return err
	}

	// 1. 收集所有唯一的日期和客户
	dates := make([]string, 0, len(stats))
	for date := range stats {
		dates = append(dates, date)
	}
	sort.Strings(dates)

	// 2. 创建表头
	headers := []string{"客户端", "vid", "cid"}
	headers = append(headers, dates...)
	headers = append(headers, "合计")

	headerRow := sheet.AddRow()
	for _, header := range headers {
		cell := headerRow.AddCell()
		cell.SetString(header)
	}

	// 3. 按批次处理客户数据
	batchSize := 100 // 每批处理的客户数量

	// 收集所有客户ID
	vids := make([]int64, 0)
	for _, dateStats := range stats {
		for vid := range dateStats {
			// 检查是否已添加
			found := false
			for _, existingVid := range vids {
				if existingVid == vid {
					found = true
					break
				}
			}
			if !found {
				vids = append(vids, vid)
			}
		}
	}

	// 按客户名称排序
	sort.Slice(vids, func(i, j int) bool {
		// 获取客户名称
		var nameI, nameJ string
		for _, dateStats := range stats {
			if stat, ok := dateStats[vids[i]]; ok && stat.CustomerName != "" {
				nameI = stat.CustomerName
				break
			}
		}
		for _, dateStats := range stats {
			if stat, ok := dateStats[vids[j]]; ok && stat.CustomerName != "" {
				nameJ = stat.CustomerName
				break
			}
		}
		return nameI < nameJ
	})

	// 用于计算每日合计
	dailyTotals := make(map[string]int64)

	// 分批处理客户
	for i := 0; i < len(vids); i += batchSize {
		end := i + batchSize
		if end > len(vids) {
			end = len(vids)
		}

		batchVids := vids[i:end]
		logx.Infof("导出客户批次 %d/%d, 客户数: %d", i/batchSize+1, (len(vids)+batchSize-1)/batchSize, len(batchVids))

		// 填充每个客户的数据行
		for _, vid := range batchVids {
			row := sheet.AddRow()

			// 获取客户信息
			var customerName string
			var cid int64
			for _, dateStats := range stats {
				if stat, ok := dateStats[vid]; ok {
					customerName = stat.CustomerName
					cid = stat.Cid
					break
				}
			}

			// 客户名称
			row.AddCell().SetString(customerName)
			// 客户 vid
			row.AddCell().SetInt64(vid)
			// 客户 cid
			row.AddCell().SetInt64(cid)

			// 每日数据
			rowTotal := int64(0)
			for _, date := range dates {
				var playCount int64 = 0
				if dateStats, ok := stats[date]; ok {
					if stat, ok := dateStats[vid]; ok {
						playCount = stat.PlayCount
					}
				}
				row.AddCell().SetInt64(playCount)
				rowTotal += playCount
				dailyTotals[date] += playCount
			}

			// 行合计
			row.AddCell().SetInt64(rowTotal)
		}

		// 定期保存文件以减少内存压力
		if (i+batchSize) < len(vids) && (i+batchSize)%1000 == 0 {
			if err := file.Save(filePath); err != nil {
				return fmt.Errorf("保存中间文件失败: %w", err)
			}
			logx.Infof("已保存中间文件，处理进度: %d/%d", i+batchSize, len(vids))
		}
	}

	if isClip != "" {
		// 添加总次数行
		totalRow := sheet.AddRow()
		totalRow.AddCell().SetString("总次数")
		totalRow.AddCell().SetString("")
		totalRow.AddCell().SetString("")
		// 填充总次数
		grandTotal := int64(0)
		for _, date := range dates {
			total := dailyTotals[date]
			totalRow.AddCell().SetInt64(total)
			grandTotal += total
		}

		// 添加总次数
		totalRow.AddCell().SetInt64(grandTotal)
	}

	// 添加合计行
	totalRow := sheet.AddRow()
	totalRow.AddCell().SetString("合计")
	totalRow.AddCell().SetString("")
	totalRow.AddCell().SetString("")

	// 填充每日合计
	grandTotal := int64(0)
	for _, date := range dates {
		total := dailyTotals[date]
		totalRow.AddCell().SetInt64(total)
		grandTotal += total
	}

	// 添加总计
	totalRow.AddCell().SetInt64(grandTotal)

	// 最终保存文件
	return file.Save(filePath)
}

func (l *ExportPlayRecordStatsLogic) ExportPlayRecordStatsV2(req *types.ExportPlayRecordStatsReq) (*types.ExportPlayRecordStatsResp, error) {
	// 设置超时控制
	ctx, cancel := context.WithTimeout(l.ctx, 30*time.Minute)
	defer cancel()

	// 创建一个完成通道
	done := make(chan struct{})
	var result *types.ExportPlayRecordStatsResp
	var exportErr error

	// 在goroutine中执行导出操作
	go func() {
		defer close(done)
		result, exportErr = l.doExportPlayRecordStats(req)
	}()

	// 等待导出完成或超时
	select {
	case <-ctx.Done():
		return nil, fmt.Errorf("导出操作超时，请尝试减少数据范围或分批导出")
	case <-done:
		return result, exportErr
	}
}

// doExportPlayRecordStats 实际执行导出逻辑
func (l *ExportPlayRecordStatsLogic) doExportPlayRecordStats(req *types.ExportPlayRecordStatsReq) (*types.ExportPlayRecordStatsResp, error) {

	// 开始监控内存
	l.logMemoryUsage("开始导出")

	// 验证用户权限
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return nil, errors.New(types.ErrUserAuthFailed)
	}

	if authUser.Role < middleware.RoleAdmin {
		return nil, middleware.ErrNoPermission
	}

	userId := authUser.UserId

	if req.PlayType != 1 && req.PlayType != 2 && req.PlayType != 3 {
		return nil, errors.New("请选择正确的播放类型导出")
	}

	// 初始化进度
	l.updateProgress(userId, ExportProgress{
		Stage:      "初始化",
		Percentage: 0,
		StartTime:  time.Now(),
	})

	// 确保导出目录存在
	exportDir := filepath.FromSlash(l.svcCtx.Config.File.ExportPath)
	if err := os.MkdirAll(exportDir, 0755); err != nil {
		return nil, errors.New(types.ErrCreateDir)
	}

	// 设置临时目录
	tempDirName := fmt.Sprintf("playRecordStats_%s", time.Now().Format("20060102150405"))
	tempDir := filepath.Join(exportDir, tempDirName)
	if err := os.MkdirAll(tempDir, 0755); err != nil {
		return nil, errors.New(types.ErrCreateDir)
	}
	defer func() {
		// 使用goroutine延迟删除临时目录，避免阻塞响应
		go func() {
			// 等待一段时间再删除，确保文件已被完全使用
			time.Sleep(5 * time.Minute)
			os.RemoveAll(tempDir)
		}()
	}()

	// 根据播放类型导出不同的数据
	var filePaths []string
	var err error
	var customerNameMap = make(map[int64]types.ExportCustomerInfo)
	var vid []int64

	// 获取客户信息
	if err := l.getCustomerInfoV2(req, &customerNameMap, &vid); err != nil {
		return nil, err
	}

	l.logMemoryUsage("获取客户信息后")

	// 验证客户数量
	if len(vid) > 1000 {
		logx.Debugf("客户数量过多: %d，可能导致导出缓慢", len(vid))
	}

	// 更新进度 - 获取客户信息
	l.updateProgress(userId, ExportProgress{
		Stage:      "获取客户信息",
		Percentage: 10,
		StartTime:  time.Now(),
	})

	// 处理时间范围
	var startTimeUTC, endTimeUTC time.Time
	var startTimeBJ, endTimeBJ time.Time

	if err := l.processTimeRangeV2(req, &startTimeUTC, &endTimeUTC, &startTimeBJ, &endTimeBJ); err != nil {
		return nil, err
	}

	// 更新进度 - 处理时间范围
	l.updateProgress(userId, ExportProgress{
		Stage:      "处理时间范围",
		Percentage: 20,
		StartTime:  time.Now(),
	})

	// 检查时间范围
	daysDiff := int(endTimeUTC.Sub(startTimeUTC).Hours()/24) + 1
	if daysDiff > 90 {
		logx.Debugf("导出时间范围过大: %d 天，可能导致内存压力", daysDiff)
	}

	// 设置文件前缀和后缀
	var prefix string
	var isClip string
	if req.PlayType == 3 { // 副歌
		isClip = "_isClip"
	}

	// 导出UTC时区数据
	logx.Infof("开始导出UTC时区数据，时间范围: %s 至 %s", startTimeUTC.Format("2006-01-02"), endTimeUTC.Format("2006-01-02"))
	statsUTC, err := l.getPlayStatsV2(startTimeUTC.Unix(), endTimeUTC.Unix(), req.Dates, req.PlayType, vid, req.UseType, types.UTC, customerNameMap)
	if err != nil {
		return nil, fmt.Errorf("获取UTC时区播放统计失败: %w", err)
	}

	l.logMemoryUsage("获取UTC数据后")

	// 更新进度 - 获取UTC数据
	l.updateProgress(userId, ExportProgress{
		Stage:      "获取UTC时区数据",
		Percentage: 30,
		StartTime:  time.Now(),
	})

	filePathUTC := filepath.Join(tempDir, fmt.Sprintf("%s%s_UTC%s.xlsx", prefix, fmt.Sprintf("%s至%s", startTimeUTC.Format("2006-01-02"), endTimeUTC.Format("2006-01-02")), isClip))
	if err := l.exportStatsToExcelV2(statsUTC, filePathUTC, isClip); err != nil {
		return nil, fmt.Errorf("导出UTC时区Excel失败: %w", err)
	}
	filePaths = append(filePaths, filePathUTC)

	l.logMemoryUsage("导出UTC Excel后")

	// 更新进度 - 导出UTC Excel
	l.updateProgress(userId, ExportProgress{
		Stage:      "导出UTC时区Excel",
		Percentage: 50,
		StartTime:  time.Now(),
	})

	// 释放内存
	statsUTC = nil
	runtime.GC()

	// 导出北京时区数据
	logx.Infof("开始导出北京时区数据，时间范围: %s 至 %s", startTimeBJ.Format("2006-01-02"), endTimeBJ.Format("2006-01-02"))
	statsBJ, err := l.getPlayStatsV2(startTimeBJ.Unix(), endTimeBJ.Unix(), req.Dates, req.PlayType, vid, req.UseType, types.BJ, customerNameMap)
	if err != nil {
		return nil, fmt.Errorf("获取北京时区播放统计失败: %w", err)
	}

	l.logMemoryUsage("获取beijing数据后")

	// 更新进度 - 获取北京时区数据
	l.updateProgress(userId, ExportProgress{
		Stage:      "获取北京时区数据",
		Percentage: 60,
		StartTime:  time.Now(),
	})

	filePathBJ := filepath.Join(tempDir, fmt.Sprintf("%s%s_BJ%s.xlsx", prefix, fmt.Sprintf("%s至%s", startTimeBJ.Format("2006-01-02"), endTimeBJ.Format("2006-01-02")), isClip))
	if err := l.exportStatsToExcelV2(statsBJ, filePathBJ, isClip); err != nil {
		return nil, fmt.Errorf("导出北京时区Excel失败: %w", err)
	}
	filePaths = append(filePaths, filePathBJ)

	l.logMemoryUsage("导出beijing Excel后")

	// 更新进度 - 导出北京时区Excel
	l.updateProgress(userId, ExportProgress{
		Stage:      "导出北京时区Excel",
		Percentage: 80,
		StartTime:  time.Now(),
	})

	// 释放内存
	statsBJ = nil
	runtime.GC()

	// 创建ZIP文件
	zipFileName := fmt.Sprintf("%s.zip", time.Now().Format("20060102150405"))
	zipFilePath := filepath.Join(exportDir, zipFileName)
	if err := l.createZipArchiveV2(zipFilePath, filePaths); err != nil {
		return nil, fmt.Errorf("创建ZIP文件失败: %w", err)
	}

	// 更新进度 - 创建ZIP文件
	l.updateProgress(userId, ExportProgress{
		Stage:      "创建ZIP文件",
		Percentage: 90,
		StartTime:  time.Now(),
	})

	// 获取文件信息
	fileInfo, err := os.Stat(zipFilePath)
	if err != nil {
		return nil, fmt.Errorf("获取ZIP文件信息失败: %w", err)
	}

	// 更新进度 - 完成
	l.updateProgress(userId, ExportProgress{
		Stage:      "完成",
		Percentage: 100,
		StartTime:  time.Now(),
	})

	return &types.ExportPlayRecordStatsResp{
		Success:     true,
		Message:     "导出成功",
		FileName:    zipFileName,
		FileSize:    fileInfo.Size(),
		DownloadUrl: zipFileName,
		RecordCount: 0,
	}, nil
}

// getCustomerInfo 获取客户信息
func (l *ExportPlayRecordStatsLogic) getCustomerInfoV2(req *types.ExportPlayRecordStatsReq, customerNameMap *map[int64]types.ExportCustomerInfo, vid *[]int64) error {
	if req.CustomerName != "" {
		customers, err := l.svcCtx.CustomerModel.FindByName(req.CustomerName)
		if err != nil {
			return errors.New("获取客户信息失败")
		}

		for _, customer := range customers {
			(*customerNameMap)[customer.Vid] = types.ExportCustomerInfo{
				Cid:  customer.Cid,
				Name: customer.Name,
			}
			*vid = append(*vid, customer.Vid)
		}
	} else if req.Vid == 0 {
		// 分页获取所有客户，避免一次性加载过多数据
		var pageSize int64 = 200
		var page int64 = 1
		for {
			offset := (page - 1) * pageSize
			customers, err := l.svcCtx.CustomerModel.FindAll(offset, pageSize)
			if err != nil {
				return errors.New("获取客户信息失败")
			}

			if len(customers) == 0 {
				break // 没有更多数据
			}

			for _, customer := range customers {
				(*customerNameMap)[customer.Vid] = types.ExportCustomerInfo{
					Cid:  customer.Cid,
					Name: customer.Name,
				}
				*vid = append(*vid, customer.Vid)
			}

			// 继续下一页
			page++

			// 如果获取的数据少于页大小，说明已经到最后一页
			if len(customers) < int(pageSize) {
				break
			}
		}
	}

	if req.Vid != 0 {
		// 获取单个客户信息
		customer, err := l.svcCtx.CustomerModel.FindOneByVid(req.Vid)
		if err != nil {
			return errors.New("获取客户信息失败")
		}

		(*customerNameMap)[customer.Vid] = types.ExportCustomerInfo{
			Cid:  customer.Cid,
			Name: customer.Name,
		}
		*vid = append(*vid, customer.Vid)
	}

	return nil
}

// processTimeRange 处理时间范围
func (l *ExportPlayRecordStatsLogic) processTimeRangeV2(req *types.ExportPlayRecordStatsReq, startTimeUTC, endTimeUTC, startTimeBJ, endTimeBJ *time.Time) error {
	var err error

	// 根据导出类型处理时间范围
	switch req.ExportType {
	case 1: // 按月份导出
		if req.Month == "" {
			return errors.New("月份不能为空")
		}
		*startTimeUTC, *endTimeUTC, err = l.validateMonth(req.Month, types.UTC)
		*startTimeBJ, *endTimeBJ, err = l.validateMonth(req.Month, types.BJ)
	case 2: // 按日期范围导出
		if req.BeginDate == "" || req.EndDate == "" {
			return errors.New("开始日期和结束日期不能为空")
		}
		*startTimeUTC, *endTimeUTC, err = l.validateDates([]string{req.BeginDate, req.EndDate}, types.UTC)
		*startTimeBJ, *endTimeBJ, err = l.validateDates([]string{req.BeginDate, req.EndDate}, types.BJ)
	case 3: // 按指定日期导出
		if len(req.Dates) == 0 {
			return errors.New("日期列表不能为空")
		}
		*startTimeUTC, *endTimeUTC, err = l.validateDates(req.Dates, types.UTC)
		*startTimeBJ, *endTimeBJ, err = l.validateDates(req.Dates, types.BJ)
	case 4: // 导出所有
		// 获取 三个月前的 一号
		currentTime := time.Now()
		threeMonthsAgo := time.Date(currentTime.Year(), currentTime.Month(), 1, 0, 0, 0, 0, currentTime.Location()).AddDate(0, middleware.MonthLimit, 0)

		*startTimeUTC, *endTimeUTC, err = l.validateDates([]string{threeMonthsAgo.Format(time.DateOnly), currentTime.Format(time.DateOnly)}, types.UTC)
		*startTimeBJ, *endTimeBJ, err = l.validateDates([]string{threeMonthsAgo.Format(time.DateOnly), currentTime.Format(time.DateOnly)}, types.BJ)
	default:
		return errors.New("不支持的导出类型")
	}

	return err
}

// createZipArchive 创建ZIP压缩文件 - 优化版本，支持大文件
func (l *ExportPlayRecordStatsLogic) createZipArchiveV2(zipPath string, filePaths []string) error {
	// 创建zip文件
	zipFile, err := os.Create(zipPath)
	if err != nil {
		return err
	}
	defer zipFile.Close()

	// 创建zip写入器
	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	// 添加文件到zip
	for _, filePath := range filePaths {
		// 使用goroutine池限制并发
		err = l.addFileToZip(zipWriter, filePath)
		if err != nil {
			return err
		}

		// 添加完成后删除源文件以节省空间
		// os.Remove(filePath) // 如果需要保留源文件，可以注释此行
	}

	return nil
}

// addFileToZip 将文件添加到ZIP中 - 优化版本，使用缓冲读取
func (l *ExportPlayRecordStatsLogic) addFileToZipV2(zipWriter *zip.Writer, filePath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 获取文件信息
	info, err := file.Stat()
	if err != nil {
		return err
	}

	// 创建zip文件头
	header, err := zip.FileInfoHeader(info)
	if err != nil {
		return err
	}

	// 使用文件名而不是完整路径
	header.Name = filepath.Base(filePath)

	// 设置压缩方法
	header.Method = zip.Deflate

	// 创建文件写入器
	writer, err := zipWriter.CreateHeader(header)
	if err != nil {
		return err
	}

	// 使用缓冲读取以提高性能
	const bufferSize = 4 * 1024 * 1024 // 4MB 缓冲区
	buf := make([]byte, bufferSize)

	for {
		n, err := file.Read(buf)
		if err != nil && err != io.EOF {
			return err
		}
		if n == 0 {
			break
		}

		if _, err := writer.Write(buf[:n]); err != nil {
			return err
		}
	}

	return nil
}

// 添加内存监控函数
func (l *ExportPlayRecordStatsLogic) logMemoryUsage(stage string) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 转换为MB
	allocMB := float64(m.Alloc) / 1024 / 1024
	totalAllocMB := float64(m.TotalAlloc) / 1024 / 1024
	sysMB := float64(m.Sys) / 1024 / 1024

	logx.Infof("内存使用情况 [%s]: 当前分配: %.2f MB, 总分配: %.2f MB, 系统: %.2f MB",
		stage, allocMB, totalAllocMB, sysMB)

	// 如果内存使用过高，触发GC
	if allocMB > 500 { // 500MB阈值
		logx.Debugf("内存使用过高，触发GC")
		runtime.GC()
	}
}

// 添加进度报告结构
type ExportProgress struct {
	Stage       string  // 当前阶段
	Percentage  float64 // 完成百分比
	CurrentItem int     // 当前处理项
	TotalItems  int     // 总项数
	StartTime   time.Time
}

// 全局进度变量 (实际应用中可以使用Redis或其他方式存储)
var exportProgressMap sync.Map

// 更新进度
func (l *ExportPlayRecordStatsLogic) updateProgress(userId int64, progress ExportProgress) {
	exportProgressMap.Store(userId, progress)

	// 计算耗时和预估剩余时间
	elapsed := time.Since(progress.StartTime)
	var remaining time.Duration
	if progress.Percentage > 0 {
		remaining = time.Duration(float64(elapsed) * (100 - progress.Percentage) / progress.Percentage)
	}

	logx.Infof("导出进度 [用户: %d] - 阶段: %s, 进度: %.2f%%, 项: %d/%d, 已用时: %s, 预计剩余: %s",
		userId, progress.Stage, progress.Percentage, progress.CurrentItem, progress.TotalItems,
		elapsed.Round(time.Second), remaining.Round(time.Second))
}

// 获取进度
func (l *ExportPlayRecordStatsLogic) GetExportProgress(userId int64) (ExportProgress, bool) {
	// 验证用户权限
	authUser := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	if authUser == nil {
		return ExportProgress{}, false
	}

	if val, ok := exportProgressMap.Load(userId); ok {
		return val.(ExportProgress), true
	}
	return ExportProgress{}, false
}

// 添加重试函数
func (l *ExportPlayRecordStatsLogic) withRetry(operation string, maxRetries int, fn func() error) error {
	var err error
	for i := 0; i < maxRetries; i++ {
		err = fn()
		if err == nil {
			return nil
		}

		logx.Debugf("%s失败(尝试 %d/%d): %v", operation, i+1, maxRetries, err)

		if i < maxRetries-1 {
			// 指数退避重试
			backoff := time.Duration(math.Pow(2, float64(i))) * time.Second
			if backoff > 30*time.Second {
				backoff = 30 * time.Second
			}
			time.Sleep(backoff)
		}
	}
	return fmt.Errorf("%s失败，已重试%d次: %w", operation, maxRetries, err)
}

/*
// 在主导出函数中使用重试机制
func (l *ExportPlayRecordStatsLogic) doExportPlayRecordStats(req *types.ExportPlayRecordStatsReq) (*types.ExportPlayRecordStatsResp, error) {
    // ... 现有代码 ...

    // 使用重试机制获取UTC数据
    var statsUTC map[string]map[int64]*types.DailyPlayStat
    err = l.withRetry("获取UTC时区数据", 3, func() error {
        var err error
        statsUTC, err = l.getPlayStats(startTimeUTC.Unix(), endTimeUTC.Unix(), req.Dates, req.PlayType, vid, req.UseType, types.UTC, customerNameMap)
        return err
    })
    if err != nil {
        return nil, err
    }

    // 使用重试机制导出Excel
    err = l.withRetry("导出UTC时区Excel", 3, func() error {
        return l.exportStatsToExcel(statsUTC, filePathUTC, isClip)
    })
    if err != nil {
        return nil, fmt.Errorf("导出UTC时区Excel失败: %w", err)
    }

    // ... 类似地处理其他操作 ...

    // 使用重试机制创建ZIP文件
    err = l.withRetry("创建ZIP文件", 3, func() error {
        return l.createZipArchive(zipFilePath, filePaths)
    })
    if err != nil {
        return nil, fmt.Errorf("创建ZIP文件失败: %w", err)
    }

    // ... 现有代码 ...
}
*/
