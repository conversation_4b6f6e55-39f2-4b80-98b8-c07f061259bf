package playrecord

import (
	"context"
	"fmt"
	"github.com/xuri/excelize/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"music/internal/svc"
	"strings"
)

type CustomRecordCountLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCustomRecordCountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CustomRecordCountLogic {
	return &CustomRecordCountLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

type SongCodeBinding struct {
	Seq      int    `json:"seq"`
	SongCode string `json:"songcode"`
}

func (l *CustomRecordCountLogic) CustomRecordExport(filepath string) (bindings []SongCodeBinding, err error) {

	// 打开 Excel 文件
	f, err := excelize.OpenFile(filepath)
	if err != nil {
		return nil, fmt.Errorf("无法打开 Excel 文件: %v", err)
	}
	defer f.Close()

	// 获取第一个工作表
	rows, err := f.GetRows("Sheet1") // 假设数据在 Sheet1 中
	if err != nil {
		return nil, fmt.Errorf("无法获取工作表: %v", err)
	}

	//var bindings []SongCodeBinding
	// 遍历行（跳过标题行）
	for i, row := range rows {
		if i == 0 { // 跳过标题行
			continue
		}
		if len(row) < 4 { // 确保有足够列（序号、歌曲名、演唱者、songcode）
			continue
		}

		// 获取序号（第 1 列）和 songcode（第 4 列）
		seq, err := parseInt(row[0])
		if err != nil {
			continue
		}
		songCodes := row[3]
		songCodes = strings.Replace(songCodes, "-", ",", -1)
		songCodes = strings.Trim(songCodes, ",")
		bindings = append(bindings, SongCodeBinding{
			Seq:      seq,
			SongCode: songCodes,
		})
	}

	f, err = excelize.OpenFile(filepath)
	fmt.Println(f)
	if err != nil {
		return nil, fmt.Errorf("无法打开 Excel 文件: %v", err)
	}
	defer f.Close()

	// 获取第一个工作表
	sheetName := f.GetSheetName(0) // 获取默认工作表名称
	if sheetName == "" {
		return nil, fmt.Errorf("工作表为空")
	}

	// 查找对应行（基于序号）
	rows, err = f.GetRows(sheetName)
	if err != nil {
		return nil, fmt.Errorf("无法获取工作表数据: %v", err)
	}

	fmt.Println(rows)
	var rowIndex int
	for i, row := range rows {
		if i == 0 {
			continue // 跳过标题行
		}
		if len(row) > 0 {
			//seq, err := parseInt(row[0])
			//if err == nil && seq == req.Seq {
			//	rowIndex = i + 1 // Excel 行号从 1 开始
			//	break
			//}
		}
	}

	if rowIndex == 0 {
		//return nil, fmt.Errorf("未找到序号 %d 的行", req.Seq)
	}
	rowIndex = 4
	// 将调用次数写入第 6 列 (F 列，索引 5)
	cell := fmt.Sprintf("F%d", rowIndex)
	if err := f.SetCellValue(sheetName, cell, 2); err != nil {
		return nil, fmt.Errorf("无法写入调用次数: %v", err)
	}

	// 保存文件
	if err := f.Save(); err != nil {
		return nil, fmt.Errorf("无法保存 Excel 文件: %v", err)
	}

	return
}

// 辅助函数：将字符串转换为整数
func parseInt(s string) (int, error) {
	if s == "" {
		return 0, fmt.Errorf("空值")
	}
	var num int
	_, err := fmt.Sscanf(s, "%d", &num)
	return num, err
}
