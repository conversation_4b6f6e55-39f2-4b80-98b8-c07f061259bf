package playrecord

import (
	"music/internal/svc"
	"music/internal/types"
	entitiesCK "music/model/entities/ck"
	entitiesKtvBasic "music/model/entities/ktv_basic"
)

func (l *ListPlayRecordGroupBySongLogic) DelRepeatData(req *types.DelUniqueIDReq) error {
	var err error
	uniqueId := req.UniqueId
	var recordData []*entitiesKtvBasic.SongPlayRecord
	query := svc.DBKtvBasic.Model(&entitiesKtvBasic.SongPlayRecord{}).Select("unique_id")
	if uniqueId != "" {
		query.Where("unique_id = ?", uniqueId)
	}
	if err = query.Group("unique_id").Having("count(unique_id) > 1").Find(&recordData).Error; err != nil {
		return err
	}
	for _, vv := range recordData {
		var uniqueData []*entitiesKtvBasic.SongPlayRecord
		if err = svc.DBKtvBasic.Model(&entitiesKtvBasic.SongPlayRecord{}).Select("id,lts,start_time").
			Where("unique_id = ?", vv.UniqueId).Find(&uniqueData).Error; err != nil {
			return err
		}
		if len(uniqueData) > 1 {
			var maxStartTime int64
			var maxLts, maxId int64
			for _, v := range uniqueData {
				if v.StartTime > maxStartTime {
					maxStartTime = v.StartTime
					maxLts = v.Lts
					maxId = v.Id
				} else if v.StartTime == maxStartTime && v.Lts > maxLts {
					maxLts = v.Lts
					maxId = v.Id
				}
			}
			if maxId == 0 {
				continue
			}
			for _, v := range uniqueData {
				if v.Id != maxId {
					svc.DBKtvBasic.Where("id = ?", v.Id).Unscoped().Delete(&entitiesKtvBasic.SongPlayRecord{})
					svc.DBCkIn.Where("id = ?", v.Id).Delete(&entitiesCK.SongPlayRecord{})
				}
			}
		}
	}
	return err
}
