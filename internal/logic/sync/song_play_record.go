package sync

import (
	"context"
	"errors"
	"fmt"
	"music/internal/svc"
	entitiesCK "music/model/entities/ck"
	entitiesKtv "music/model/entities/ktv"
	entitiesKtvBasic "music/model/entities/ktv_basic"
	"time"

	"github.com/jinzhu/copier"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type SyncLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSyncLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SyncLogic {
	return &SyncLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (s *SyncLogic) SongPlayRecordToCK() error {
	lockKey := "SongPlayRecordToCK"
	ok, err := s.svcCtx.Redis.Setnx(lockKey, time.Now().String())
	if err != nil {
		logx.Errorf("SongPlayRecordToCK 获取锁失败: %v", err)
		return err
	}
	if !ok {
		logx.Erro<PERSON>("SongPlayRecordToCK 获取锁失败: %v", err)
		return errors.New("SongPlayRecordToCK 获取锁失败")
	}
	s.svcCtx.Redis.Expire(lockKey, 36000)
	// 分批同步，避免内存压力
	batchSize := 20000
	for {
		var ckInfo entitiesCK.SongPlayRecord
		if err = s.svcCtx.DBCkIn.Model(entitiesCK.SongPlayRecord{}).Select("max(id) as id").Take(&ckInfo).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("查询ClickHouse数据失败: %w", err)
			}
		}
		maxID := ckInfo.ID

		//var cnt int64
		//s.svcCtx.DBKtvBasic.Model(entitiesCK.SongPlayRecord{}).Count(&cnt)
		//fmt.Println(cnt)

		// 从MySQL查询数据
		var records []entitiesKtvBasic.SongPlayRecord
		err = s.svcCtx.DBKtvBasic.Where("id > ?", maxID).Order("id asc").Limit(batchSize).Find(&records).Error
		if err != nil {
			return fmt.Errorf("查询MySQL数据失败: %w", err)
		}

		if len(records) == 0 {
			s.svcCtx.Redis.Del(lockKey)
			break
		}

		var ckRecord []entitiesCK.SongPlayRecord
		_ = copier.Copy(&ckRecord, &records)
		// 插入到ClickHouse
		err = s.svcCtx.DBCkIn.CreateInBatches(&ckRecord, len(ckRecord)).Error
		if err != nil {
			return fmt.Errorf("插入ClickHouse失败: %w", err)
		}

		logx.Infof("同步了 %d 条记录", len(records))
		// 缓冲执行
		time.Sleep(100 * time.Millisecond)
	}
	return nil
}

func (s *SyncLogic) SongToCK() error {
	//FIXME 定义删除全部数据重新同步
	lockKey := "SongToCK"
	ok, err := s.svcCtx.Redis.Setnx(lockKey, time.Now().String())
	if err != nil {
		logx.Errorf("SongToCK 获取锁失败: %v", err)
		return err
	}
	if !ok {
		logx.Errorf("SongToCK 获取锁失败: %v", err)
		return errors.New("SongToCK 获取锁失败")
	}
	s.svcCtx.Redis.Expire(lockKey, 36000)
	// 分批同步，避免内存压力
	batchSize := 20000
	for {
		var ckInfo entitiesCK.Song
		if err = s.svcCtx.DBCkIn.Model(entitiesCK.Song{}).Select("max(id) as id").Take(&ckInfo).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("查询ClickHouse数据失败: %w", err)
			}
		}
		maxID := ckInfo.ID

		//var cnt int64
		//s.svcCtx.DBKtvBasic.Model(entitiesCK.SongPlayRecord{}).Count(&cnt)
		//fmt.Println(cnt)

		// 从MySQL查询数据
		var records []entitiesKtv.Song
		err = s.svcCtx.DBKtv.Where("id > ?", maxID).Order("id asc").Limit(batchSize).Find(&records).Error
		if err != nil {
			return fmt.Errorf("查询MySQL数据失败: %w", err)
		}

		if len(records) == 0 {
			s.svcCtx.Redis.Del(lockKey)
			break
		}

		var ckRecord []entitiesCK.Song
		_ = copier.Copy(&ckRecord, &records)
		// 插入到ClickHouse
		err = s.svcCtx.DBCkIn.CreateInBatches(&ckRecord, len(ckRecord)).Error
		if err != nil {
			return fmt.Errorf("插入ClickHouse失败: %w", err)
		}

		logx.Infof("同步了 %d 条记录", len(records))
		// 缓冲执行
		time.Sleep(100 * time.Millisecond)
	}
	return nil
}
