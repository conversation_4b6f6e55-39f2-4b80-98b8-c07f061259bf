package export

import (
	"context"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"github.com/xuri/excelize/v2"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
	"log"
	"math"
	"music/internal/logic/user"
	"music/internal/svc"
	"music/internal/types"
	"music/internal/utils"
	entitiesCK "music/model/entities/ck"
	entitiesKtv "music/model/entities/ktv"
	entitiesKtvBasic "music/model/entities/ktv_basic"
	"time"
)

const (
	//FIXME
	//RowsPerFile = 5_00_000
	RowsPerFile = 5_00_000
)

type LogicExport struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewLogicExport(ctx context.Context, svcCtx *svc.ServiceContext) *LogicExport {
	return &LogicExport{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LogicExport) RecordDetailExport(query *gorm.DB, total int64) (err error) {
	fileIndex := 1
	rowCount := 0
	//aa := l.ctx.Value(types.AuthUserKey).(*types.AuthUser)
	//fmt.Println(aa)
	newAuthUser := user.NewAuthInfoLogic(l.ctx, l.svcCtx)
	authUser, _ := newAuthUser.UserInfo()
	newFile := utils.NewFiles()

	f := l.newExcelFile()
	streamWriter := &excelize.StreamWriter{}
	streamWriter, err = f.NewStreamWriter("Sheet1")
	if err != nil {
		err = errors.New(fmt.Sprintf("创建 stream writer 失败: %s", err.Error()))
		return
	}
	headers := []interface{}{"requestid", "歌曲名称", "歌曲编号", "版权方ID", "歌曲时长", "播放时长", "开始时间", "结束时间", "停播位置", "有效次数", "场景类型", "记录类型", "播放类型", "是否免费", "客户名称", "客户应用vid", "用户类型"}
	if err := streamWriter.SetRow("A1", headers); err != nil {
		log.Fatalf("写入标题失败: %v", err)
	}
	fileName := authUser.Username + "-" + time.Now().Format("20060102")
	dirPath := fmt.Sprintf("./file/exports/song_detail/%v/%s/%s", authUser.UserId, time.Now().Format("20060102150405"), fileName)
	logUuid := uuid.NewString()
	filesNum := int(math.Ceil(float64(total) / float64(RowsPerFile)))
	svc.DBKtvBasic.Model(&entitiesKtvBasic.ExportLog{}).Create(&entitiesKtvBasic.ExportLog{
		Uuid:      logUuid,
		UserID:    fmt.Sprint(authUser.UserId),
		UserName:  authUser.Username,
		Filename:  fileName,
		TotalFile: filesNum,
		State:     entitiesKtvBasic.ExportLogStateRunning,
		DoneTime:  "",
		OssUrl:    "",
		Schedule:  0,
	})
	var customerData []*entitiesKtv.Customer
	if err = svc.DBKtv.Model(entitiesKtv.Customer{}).Select("vid,name").Find(&customerData).Error; err != nil {
		l.Logger.Errorf("ExportDetail.customerData err:%v", err)
		err = errors.New(types.ErrSelect)
		return
	}
	var customerMap = make(map[int64]*entitiesKtv.Customer, len(customerData))
	for _, customInfo := range customerData {
		customerMap[customInfo.Vid] = &entitiesKtv.Customer{
			Name: customInfo.Name,
			Vid:  customInfo.Vid,
			Cid:  customInfo.Cid,
		}
	}
	customerData = nil
	baseQuery := query.Session(&gorm.Session{})
	var lastID uint64 = 0
	// FIXME
	//filesNum = 1
	for i := 1; i <= filesNum; i++ {
		fmt.Println("查询数据开始" + time.Now().Format("2006-01-02 15:04:05"))
		var data []entitiesCK.SongPlayRecordAndSong
		currentQuery := baseQuery.Where("song_play_record.id > ?", lastID).
			Order("song_play_record.id asc").
			Limit(RowsPerFile)
		if err = currentQuery.Find(&data).Error; err != nil {
			l.Logger.Errorf("ExportDetail.data err:%v", err)
			err = errors.New(types.ErrSelect)
			return
		}
		fmt.Println("查询数据结束" + time.Now().Format("2006-01-02 15:04:05"))
		if len(data) == 0 {
			break
		}
		lastID = data[len(data)-1].ID
		fmt.Println("数据写入开始" + time.Now().Format("2006-01-02 15:04:05"))
		// 批量准备所有行数据
		var batchRows [][]interface{}
		for _, v := range data {
			v.CustomerName = customerMap[v.Vid].Name
			row := []interface{}{v.UniqueID, v.SongName, v.SongCode, v.CpID, v.SongDuration, v.PlayDuration,
				v.StartTime, v.EndTime, v.PlaybackPos, v.PlayNum, entitiesCK.SceneTypeMap[v.SceneType],
				entitiesCK.RecordTypeMap[v.RecordType], entitiesCK.PlayTypeMap[v.PlayType],
				entitiesCK.FreeTypeMap[v.FreeType], v.CustomerName, v.Vid, entitiesCK.UserTypeMap[v.UseType]}
			batchRows = append(batchRows, row)
		}

		// 批量写入
		for ii, row := range batchRows {
			rowInSheet := rowCount + ii + 2
			cellRef, _ := excelize.CoordinatesToCellName(1, rowInSheet)
			if err = streamWriter.SetRow(cellRef, row); err != nil {
				return errors.New(fmt.Sprintf("写入数据失败: %s", err.Error()))
			}
		}
		rowCount += len(batchRows)
		if rowCount >= RowsPerFile || i == filesNum {
			streamWriter.Flush()
			_, _ = newFile.CheckDirPath(dirPath, true)
			err = f.SaveAs(fmt.Sprintf(dirPath+"/调用详情模板-%d.xlsx", fileIndex))
			if err != nil {
				return errors.New("保存文件失败")
			}
			fmt.Printf("导出第 %d 个文件完成\n", fileIndex)
			svc.DBKtvBasic.Model(entitiesKtvBasic.ExportLog{}).Where("uuid = ?", logUuid).Updates(map[string]interface{}{
				"schedule": math.Round(float64(fileIndex)/float64(filesNum)*100) / 100,
			})
			fileIndex++
			rowCount = 0
			if i != filesNum {
				f = l.newExcelFile()
				streamWriter, _ = f.NewStreamWriter("Sheet1")
				streamWriter.SetRow("A1", headers)
			}
		}
		fmt.Println("数据写入结束" + time.Now().Format("2006-01-02 15:04:05"))
	}
	err = newFile.ZipFolder(dirPath, dirPath+".zip")
	fmt.Println("全部导出完成")
	_, _ = l.svcCtx.Redis.Del(entitiesKtvBasic.LockKeySongDetailExport)
	svc.DBKtvBasic.Model(entitiesKtvBasic.ExportLog{}).Where("uuid = ?", logUuid).Updates(map[string]interface{}{
		"state":     entitiesKtvBasic.ExportLogStateDone,
		"done_time": time.Now().Format("2006-01-02 15:04:05"),
		"oss_url":   dirPath + ".zip",
	})
	return
}

func (l *LogicExport) newExcelFile() *excelize.File {
	f := excelize.NewFile()
	// 删除默认 Sheet1 之外的其他表（如 Sheet2, Sheet3）
	for _, name := range f.GetSheetList() {
		if name != "Sheet1" {

			f.DeleteSheet(name)
		}
	}
	return f
}
