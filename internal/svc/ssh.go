package svc

import (
	"fmt"
	"golang.org/x/crypto/ssh"
	"gorm.io/driver/mysql"
	sql "github.com/go-sql-driver/mysql"
	"gorm.io/gorm"
	"net"
	"os"
	"strconv"
	"time"
	gormLogger "gorm.io/gorm/logger"
)

var dial *ssh.Client

var DBKtvSshIn *gorm.DB

type Dialer struct {
	client *ssh.Client
}

type SSH struct {
	Host     string `json:"host"`
	User     string `json:"user"`
	Port     int    `json:"port"`
	Type     string `json:"type"`
	Password string `json:"password"`
	KeyFile  string `json:"key"`
}

type MySQL struct {
	Host     string `json:"host"`
	User     string `json:"user"`
	Port     int    `json:"port"`
	Password string `json:"password"`
	Database string `json:"database"`
}

func (v *Dialer) Dial(address string) (net.Conn, error) {
	return v.client.Dial("tcp", address)
}

func (s *SSH) DialWithPassword() (*ssh.Client, error) {
	address := fmt.Sprintf("%s:%d", s.Host, s.Port)
	config := &ssh.ClientConfig{
		User: s.User,
		Auth: []ssh.AuthMethod{
			ssh.Password(s.Password),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}
	return ssh.Dial("tcp", address, config)
}

func (s *SSH) DialWithKeyFile() (*ssh.Client, error) {
	address := fmt.Sprintf("%s:%d", s.Host, s.Port)
	config := &ssh.ClientConfig{
		User:            s.User,
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}
	if k, err := os.ReadFile(s.KeyFile); err != nil {
		return nil, err
	} else {
		signer, err := ssh.ParsePrivateKey(k)
		if err != nil {
			return nil, err
		}
		config.Auth = []ssh.AuthMethod{
			ssh.PublicKeys(signer),
		}
	}
	return ssh.Dial("tcp", address, config)
}

func (m *MySQL) New() (db *gorm.DB, err error) {
	// 填写注册的mysql网络
	dsn := fmt.Sprintf("%s:%s@mysql+ssh(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		m.User, m.Password, m.Host, m.Port, m.Database)
	db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{Logger: gormLogger.Default.LogMode(gormLogger.Info)})
	if err != nil {
		return
	}
	sqlDb, _ := db.DB()
	sqlDb.SetMaxIdleConns(20)
	sqlDb.SetMaxOpenConns(30)
	sqlDb.SetConnMaxLifetime(time.Second * 30)
	return
}

func sshClient() (err error) {
	port, _ := strconv.Atoi("22")
	client := SSH{
		Host:     "*************",
		User:     "root",
		Port:     port,
		Password: "eyNZ-ZFN:7MzYJd",
		Type:     "PASSWORD", // PASSWORD or KEY
	}
	dial, err = client.DialWithPassword()
	if err != nil {
		return
	}
	// 注册ssh代理
	sql.RegisterDial("mysql+ssh", (&Dialer{client: dial}).Dial)
	return
}

func DBKtvSshInConnect() (db *gorm.DB, err error) {
	if dial == nil {
		if err = sshClient(); err != nil {
			return
		}
	}
	port, _ := strconv.Atoi("3306")
	my := MySQL{
		Host:     "rm-7xvw6sojr7n6781ua.mysql.rds.aliyuncs.com",
		User:     "ktv_user",
		Password: "ZAQ!2wsx",
		Port:     port,
		Database: "ktv-basic",
	}
	DBKtvSshIn, err = my.New()
	if err != nil {
		fmt.Println("mysql connect error:", err)
		return
	}
	db = DBKtvSshIn
	return
}
