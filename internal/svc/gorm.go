package svc

import (
	"music/internal/config"
	entitiesKtvBasic "music/model/entities/ktv_basic"
	"os"
	"time"

	"go.uber.org/zap"
	"gorm.io/driver/clickhouse"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

var DBKtv, DBKtvBasic, DBCkIn *gorm.DB

func DBInit(config config.Config) {
	DBKtv = loadMysqlConn(config.MysqlOut.DataSource)
	//DBKtvBasic = loadMysqlConn(config.MysqlIn.DataSource)
	DBKtvBasic, _ = DBKtvSshInConnect() //正式
	DBCkIn = loadCKConn(config.CkIn.DataSource)
	DBMigration(DBKtvBasic)
}

func loadMysqlConn(conn string) *gorm.DB {
	var ormLogger logger.Interface
	//if gin.Mode() == "debug" {
	ormLogger = logger.Default.LogMode(logger.Info)
	//} else {
	//	ormLogger = logger.Default
	//}
	db, err := gorm.Open(mysql.New(mysql.Config{
		DSN:                       conn,  // DSN data source name
		DefaultStringSize:         256,   // string 类型字段的默认长度
		DisableDatetimePrecision:  true,  // 禁用 datetime 精度，MySQL 5.6 之前的数据库不支持
		DontSupportRenameIndex:    true,  // 重命名索引时采用删除并新建的方式，MySQL 5.7 之前的数据库和 MariaDB 不支持重命名索引
		DontSupportRenameColumn:   true,  // 用 `change` 重命名列，MySQL 8 之前的数据库和 MariaDB 不支持重命名列
		SkipInitializeWithVersion: false, // 根据版本自动配置
	}), &gorm.Config{
		Logger: ormLogger,
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
		},
		DisableForeignKeyConstraintWhenMigrating: true,
	})
	if err != nil {
		panic(err)
	}
	sqlDB, _ := db.DB()
	sqlDB.SetMaxIdleConns(20)  //设置连接池，空闲
	sqlDB.SetMaxOpenConns(100) //打开
	sqlDB.SetConnMaxLifetime(time.Second * 30)
	return db
}

func loadCKConn(conn string) *gorm.DB {
	//conn = "tcp://localhost:9000?database=ktv-basic&username=default&password=123456"
	//conn = "clickhouse://default:123456@localhost:9000/ktv-basic?dial_timeout=10s&read_timeout=20s"
	var ormLogger logger.Interface
	//if gin.Mode() == "debug" {
	ormLogger = logger.Default.LogMode(logger.Info)
	db, err := gorm.Open(clickhouse.Open(conn), &gorm.Config{
		Logger: ormLogger,
	})
	if err != nil {
		panic(err)
	}
	return db
}

func DBMigration(db *gorm.DB) {
	err := db.AutoMigrate(
		entitiesKtvBasic.ExportLog{},
	)
	if err != nil {
		zap.L().Error("register table fail--", zap.Error(err))
		os.Exit(0)
	}
	return
}
