package scheduler

// SchedulerConfig 定时器配置
type SchedulerConfig struct {
	Enabled bool                   `json:"enabled" yaml:"enabled"`                 // 是否启用定时器服务
	Tasks   map[string]*TaskConfig `json:"tasks,omitempty" yaml:"tasks,omitempty"` // 任务配置
}

// TaskConfig 任务配置
type TaskConfig struct {
	ID       string `json:"id" yaml:"id"`             // 任务ID
	Name     string `json:"name" yaml:"name"`         // 任务名称
	CronSpec string `json:"cronSpec" yaml:"cronSpec"` // cron表达式
	Enabled  bool   `json:"enabled" yaml:"enabled"`   // 是否启用
	Type     string `json:"type" yaml:"type"`         // 任务类型
}
