package scheduler

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
	"github.com/zeromicro/go-zero/core/logx"
	"go.uber.org/atomic"
)

// TaskFunc 定时任务函数类型
type TaskFunc func() error

// Task 定时任务结构
type Task struct {
	ID       string   // 任务ID
	Name     string   // 任务名称
	CronSpec string   // cron表达式
	Func     TaskFunc // 执行函数
	Enabled  bool     // 是否启用
}

// SchedulerService 定时任务调度服务
type SchedulerService struct {
	cron     *cron.Cron
	tasks    map[string]*Task
	entries  map[string]cron.EntryID // 任务ID到cron条目ID的映射
	mutex    sync.RWMutex
	running  atomic.Bool
	stopChan chan struct{}
	wg       sync.WaitGroup
}

// NewSchedulerService 创建新的调度服务
func NewSchedulerService() *SchedulerService {
	return &SchedulerService{
		cron:     cron.New(cron.WithSeconds()), // 支持秒级定时
		tasks:    make(map[string]*Task),
		entries:  make(map[string]cron.EntryID),
		stopChan: make(chan struct{}),
	}
}

// AddTask 添加定时任务
func (s *SchedulerService) AddTask(task *Task) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if task.ID == "" {
		return fmt.Errorf("任务ID不能为空")
	}

	if task.CronSpec == "" {
		return fmt.Errorf("cron表达式不能为空")
	}

	if task.Func == nil {
		return fmt.Errorf("任务函数不能为空")
	}

	// 检查任务是否已存在
	if _, exists := s.tasks[task.ID]; exists {
		return fmt.Errorf("任务ID %s 已存在", task.ID)
	}

	// 包装任务函数，添加错误处理和日志
	wrappedFunc := func() {
		logx.Infof("开始执行定时任务: %s (%s)", task.Name, task.ID)
		start := time.Now()
		
		if err := task.Func(); err != nil {
			logx.Errorf("定时任务执行失败: %s (%s), 错误: %v", task.Name, task.ID, err)
		} else {
			logx.Infof("定时任务执行成功: %s (%s), 耗时: %v", task.Name, task.ID, time.Since(start))
		}
	}

	// 添加到cron调度器
	entryID, err := s.cron.AddFunc(task.CronSpec, wrappedFunc)
	if err != nil {
		return fmt.Errorf("添加cron任务失败: %w", err)
	}

	// 保存任务信息
	s.tasks[task.ID] = task
	s.entries[task.ID] = entryID

	logx.Infof("定时任务已添加: %s (%s), cron: %s", task.Name, task.ID, task.CronSpec)
	return nil
}

// RemoveTask 移除定时任务
func (s *SchedulerService) RemoveTask(taskID string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	task, exists := s.tasks[taskID]
	if !exists {
		return fmt.Errorf("任务ID %s 不存在", taskID)
	}

	entryID, exists := s.entries[taskID]
	if exists {
		s.cron.Remove(entryID)
		delete(s.entries, taskID)
	}

	delete(s.tasks, taskID)
	logx.Infof("定时任务已移除: %s (%s)", task.Name, taskID)
	return nil
}

// EnableTask 启用任务
func (s *SchedulerService) EnableTask(taskID string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	task, exists := s.tasks[taskID]
	if !exists {
		return fmt.Errorf("任务ID %s 不存在", taskID)
	}

	if task.Enabled {
		return nil // 已经启用
	}

	// 重新添加到cron调度器
	wrappedFunc := func() {
		logx.Infof("开始执行定时任务: %s (%s)", task.Name, task.ID)
		start := time.Now()
		
		if err := task.Func(); err != nil {
			logx.Errorf("定时任务执行失败: %s (%s), 错误: %v", task.Name, task.ID, err)
		} else {
			logx.Infof("定时任务执行成功: %s (%s), 耗时: %v", task.Name, task.ID, time.Since(start))
		}
	}

	entryID, err := s.cron.AddFunc(task.CronSpec, wrappedFunc)
	if err != nil {
		return fmt.Errorf("启用任务失败: %w", err)
	}

	s.entries[taskID] = entryID
	task.Enabled = true

	logx.Infof("定时任务已启用: %s (%s)", task.Name, taskID)
	return nil
}

// DisableTask 禁用任务
func (s *SchedulerService) DisableTask(taskID string) error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	task, exists := s.tasks[taskID]
	if !exists {
		return fmt.Errorf("任务ID %s 不存在", taskID)
	}

	if !task.Enabled {
		return nil // 已经禁用
	}

	entryID, exists := s.entries[taskID]
	if exists {
		s.cron.Remove(entryID)
		delete(s.entries, taskID)
	}

	task.Enabled = false
	logx.Infof("定时任务已禁用: %s (%s)", task.Name, taskID)
	return nil
}

// Start 启动调度服务
func (s *SchedulerService) Start() error {
	if !s.running.CompareAndSwap(false, true) {
		return fmt.Errorf("调度服务已在运行")
	}

	s.cron.Start()
	logx.Info("定时任务调度服务已启动")

	s.wg.Add(1)
	go func() {
		defer s.wg.Done()
		<-s.stopChan
		logx.Info("定时任务调度服务收到停止信号")
	}()

	return nil
}

// Stop 停止调度服务
func (s *SchedulerService) Stop() {
	if s.running.Load() {
		s.cron.Stop()
		close(s.stopChan)
		s.wg.Wait()
		s.running.Store(false)
		logx.Info("定时任务调度服务已停止")
	}
}

// IsRunning 检查服务是否正在运行
func (s *SchedulerService) IsRunning() bool {
	return s.running.Load()
}

// GetTasks 获取所有任务信息
func (s *SchedulerService) GetTasks() map[string]*Task {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	result := make(map[string]*Task)
	for id, task := range s.tasks {
		result[id] = task
	}
	return result
}

// GetTaskInfo 获取任务详细信息
func (s *SchedulerService) GetTaskInfo(taskID string) (*Task, error) {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	task, exists := s.tasks[taskID]
	if !exists {
		return nil, fmt.Errorf("任务ID %s 不存在", taskID)
	}

	return task, nil
}

// RunTaskOnce 立即执行一次任务（不影响定时调度）
func (s *SchedulerService) RunTaskOnce(taskID string) error {
	s.mutex.RLock()
	task, exists := s.tasks[taskID]
	s.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("任务ID %s 不存在", taskID)
	}

	logx.Infof("手动执行定时任务: %s (%s)", task.Name, task.ID)
	start := time.Now()
	
	if err := task.Func(); err != nil {
		logx.Errorf("手动执行任务失败: %s (%s), 错误: %v", task.Name, task.ID, err)
		return err
	}
	
	logx.Infof("手动执行任务成功: %s (%s), 耗时: %v", task.Name, task.ID, time.Since(start))
	return nil
}

// StartWithContext 使用context启动调度服务
func (s *SchedulerService) StartWithContext(ctx context.Context) error {
	if err := s.Start(); err != nil {
		return err
	}

	go func() {
		<-ctx.Done()
		s.Stop()
	}()

	return nil
}
