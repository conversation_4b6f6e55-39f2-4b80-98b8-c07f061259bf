package scheduler

import (
	"context"
	"fmt"
	"music/internal/logic/playrecord"
	syncLogic "music/internal/logic/sync"
	"music/internal/svc"
	"music/internal/types"
	"sync"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

var SchedulerSer *SchedulerService

func TaskInit(svc *svc.ServiceContext) {
	var err error
	SchedulerSer = createSchedulerService(context.Background())
	taskAdapter := func() error {
		return DataCleanupTask(context.Background(), svc)
	}
	taskSyncMysqlCk := func() error {
		return SyncMysqlCkTask(context.Background(), svc)
	}
	err = SchedulerSer.AddTask(&Task{
		ID:       "DelUniqueId",
		Name:     "删除重复数据",
		CronSpec: "0 0 15 * * *", // 每天15点执行
		Func:     taskAdapter,
		Enabled:  true,
	})

	// 每分钟执行一次
	err = SchedulerSer.AddTask(&Task{
		ID:       "syncMysqlCK",
		Name:     "同步song数据",
		CronSpec: "0 * * * * *",
		//CronSpec: "0 0/30 * * * *",
		Func:    taskSyncMysqlCk,
		Enabled: false, //FIXME
	})
	fmt.Println(err)
}

// 创建定时器服务
func createSchedulerService(ctx context.Context) *SchedulerService {
	// 创建定时器服务
	schedulerService := NewSchedulerService()
	if err := schedulerService.StartWithContext(ctx); err != nil {
		logx.Errorf("启动定时器服务失败: %v", err)
		return nil
	}
	logx.Info("定时器服务已启动")
	return schedulerService
}

// DataCleanupTask 删除重复数据的任务
func DataCleanupTask(ctx context.Context, svcCtx *svc.ServiceContext) error {
	fmt.Println("开始删除重复数据--", time.Now().Format("2006-01-02 15:04:05"))
	fmt.Println("开始删除重复数据")
	l := playrecord.NewListPlayRecordGroupBySongLogic(ctx, svcCtx)

	// 调用删除重复数据的方法
	req := &types.DelUniqueIDReq{}
	err := l.DelRepeatData(req)
	if err != nil {
		logx.Errorf("删除重复数据失败: %v", err)
		return err
	}
	logx.Info("删除重复数据任务执行成功")
	return nil
}

func SyncMysqlCkTask(ctx context.Context, svcCtx *svc.ServiceContext) error {
	fmt.Println("开始同步数据-start-", time.Now().Format("2006-01-02 15:04:05"))
	var err error
	// 调用同步逻辑
	var wg sync.WaitGroup
	syncLogic := syncLogic.NewSyncLogic(ctx, svcCtx)
	wg.Add(2)
	go func() {
		defer wg.Done()
		err = syncLogic.SongPlayRecordToCK()
		if err != nil {
			logx.Errorf("同步数据到ClickHouse失败: %v", err)
		}
	}()

	go func() {
		defer wg.Done()
		err = syncLogic.SongToCK()
		if err != nil {
			logx.Errorf("同步数据到ClickHouse失败: %v", err)
			return
		}
	}()
	wg.Wait()
	logx.Info("同步数据到ClickHouse成功")
	fmt.Println("同步数据完成-end-", time.Now().Format("2006-01-02 15:04:05"))
	return nil
}
