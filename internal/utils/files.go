package utils

import (
	"archive/zip"
	"errors"
	"io"
	"os"
	"path/filepath"
)

type Files struct {
}

func NewFiles() *Files {
	return &Files{}
}

func (f *Files) CheckDirPath(path string, create bool) (exists bool, err error) {
	exists = false
	if path == "" {
		err = errors.New("目录不能为空")
		return
	}
	if _, err = os.Stat(path); os.IsNotExist(err) {
		if !create {
			return
		}
		if err = os.MkdirAll(path, os.ModePerm); err != nil {
			return
		}
	}
	exists = true
	return
}

func (f *Files) ZipFolder(srcFolder, zipFileName string) error {
	// 创建 ZIP 文件
	zipFile, err := os.Create(zipFileName)
	if err != nil {
		return err
	}
	defer zipFile.Close()

	// 创建 ZIP 写入器
	archive := zip.NewWriter(zipFile)
	defer archive.Close()

	// 遍历文件夹
	err = filepath.Walk(srcFolder, func(filePath string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// 创建 ZIP 文件中的文件头
		header, err := zip.FileInfoHeader(info)
		if err != nil {
			return err
		}

		// 设置相对路径
		header.Name, err = filepath.Rel(srcFolder, filePath)
		if err != nil {
			return err
		}

		// 如果是目录，添加斜杠
		if info.IsDir() {
			header.Name += "/"
		} else {
			// 设置压缩方法
			header.Method = zip.Deflate
		}

		// 创建 ZIP 中的文件
		writer, err := archive.CreateHeader(header)
		if err != nil {
			return err
		}

		// 如果不是目录，写入文件内容
		if !info.IsDir() {
			file, err := os.Open(filePath)
			if err != nil {
				return err
			}
			defer file.Close()

			_, err = io.Copy(writer, file)
			if err != nil {
				return err
			}
		}

		return nil
	})

	return err
}
