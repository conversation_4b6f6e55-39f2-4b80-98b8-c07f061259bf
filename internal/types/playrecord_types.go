package types

import "time"

// 播放记录相关类型
type (
	// 播放记录信息
	PlayRecordInfo struct {
		Id           int64     `json:"id"`
		Lts          int64     `json:"lts"`
		Vid          int64     `json:"vid"`
		UniqueId     string    `json:"uniqueId"`
		CpId         int64     `json:"cpId"`
		SongCode     int64     `json:"songCode"`
		SongName     string    `json:"songName,optional"`   // 冗余字段，便于前端展示
		SongSinger   string    `json:"songSinger,optional"` // 冗余字段，便于前端展示
		SongDuration int       `json:"songDuration"`
		PlayDuration int       `json:"playDuration"`
		StartTime    int64     `json:"startTime"`
		EndTime      int64     `json:"endTime"`
		PlaybackPos  int       `json:"playbackPos"`
		PlayNum      int       `json:"playNum"`
		SceneType    int       `json:"sceneType"`
		PlayType     int       `json:"playType"`
		FreeType     int       `json:"freeType"`
		RecordType   int       `json:"recordType"`
		UseType      int       `json:"useType"`
		PushStatus   int       `json:"pushStatus"`
		PushTime     int64     `json:"pushTime"`
		CreateTime   time.Time `json:"createTime"`
	}

	// 添加播放记录请求
	CreatePlayRecordReq struct {
		Lts          int64  `json:"lts"`
		Vid          int64  `json:"vid"`
		UniqueId     string `json:"uniqueId"`
		RequestIp    string `json:"requestIp,optional"`
		CpId         int64  `json:"cpId"`
		SongCode     int64  `json:"songCode"`
		SongDuration int    `json:"songDuration"`
		PlayDuration int    `json:"playDuration"`
		StartTime    int64  `json:"startTime"`
		EndTime      int64  `json:"endTime"`
		PlaybackPos  int    `json:"playbackPos"`
		PlayNum      int    `json:"playNum"`
		SceneType    int    `json:"sceneType"`
		PlayType     int    `json:"playType"`
		FreeType     int    `json:"freeType"`
		RecordType   int    `json:"recordType"`
		UseType      int    `json:"useType"`
		Uid          string `json:"uid,optional"`
		ChannelId    string `json:"channelId,optional"`
	}

	// 添加播放记录响应
	CreatePlayRecordResp struct {
		Id      int64  `json:"id"`
		Success bool   `json:"success"`
		Message string `json:"message,optional"`
	}

	// 查询播放记录请求 - 增加更多查询条件
	_ListPlayRecordReq struct {
		Vid        int64  `form:"vid,optional"`        // 客户ID
		SongCode   int64  `form:"songCode,optional"`   // 歌曲编号
		SongName   string `form:"songName,optional"`   // 歌曲名称
		Singer     string `form:"singer,optional"`     // 歌手名称
		CpId       int64  `form:"cpId,optional"`       // 版权方ID
		VendorName string `form:"vendorName,optional"` // 版权方名称
		PlayType   int    `form:"playType,optional"`   // 播放类型
		FreeType   int    `form:"freeType,optional"`   // 免费类型
		RecordType int    `form:"recordType,optional"` // 记录类型
		UseType    int    `form:"useType,optional"`    // 用户类型
		SceneType  int    `form:"sceneType,optional"`  // 场景类型
		PushStatus int    `form:"pushStatus,optional"` // 推送状态
		StartDate  string `form:"startDate,optional"`  // 开始日期 YYYY-MM-DD
		EndDate    string `form:"endDate,optional"`    // 结束日期 YYYY-MM-DD
		Page       int64  `form:"page,default=1"`
		PageSize   int64  `form:"pageSize,default=10"`
	}

	SumPlayRecordReq struct {
		StartDate       string   `json:"startDate"` // 格式：YYYY-MM-DD
		EndDate         string   `json:"endDate"`   // 格式：YYYY-MM-DD
		Month           string   `json:"month"`     // 格式：YYYY-MM
		PlayType        int      `json:"playType,optional"`
		CustomerName    string   `json:"customerName,optional"`
		UseType         int64    `json:"useType,optional"`
		Vid             int64    `json:"vid,optional"`
		SongCode        int64    `json:"songCode,optional"`
		SongName        string   `json:"songName,optional"`
		SceneType       int      `json:"sceneType,optional"`       //场景类型  1 秀场直播背景音乐 2 秀场直播背景音乐  3 语聊场景K歌 4 语聊场景背景音乐 5 VR场景K歌 6 语聊-合唱（≤20 人）  7 语聊-合唱（＞20 人）
		PlayDuration    int      `json:"playDuration,optional"`    // 播放时长
		TimeZone        string   `json:"timeZone,optional"`        // 时区 Shanghai UTC
		CpID            int64    `json:"cpId,optional"`            //版权方ID
		Singer          string   `json:"singer,optional"`          // 歌手
		LyricType       []string `json:"lyricType,optional"`       // 歌词类型  歌词类型：0 zip格式，1 lrc格式，2 vtt格式。可多个，如"1,2"
		Pitchs          string   `json:"pitchs,optional"`          // 打分类型  可多个，如"1,2"
		HighPartType    int      `json:"highPartType,optional"`    // 副歌类型  1无副歌，2机器校验，3人工校验
		TagIDs          string   `json:"tagIds,optional"`          // 标签 标签id，可多个，如“1,2,3”
		Status          int      `json:"status,optional"`          // 状态 0:下架, 1:上架
		CreateTimeStart string   `json:"createTimeStart,optional"` // 上架开始时间
		CreateTimeEnd   string   `json:"createTimeEnd,optional"`   // 上架结束时间
		CloseTimeStart  string   `json:"closeTimeStart,optional"`  // 下架开始时间
		CloseTimeEnd    string   `json:"closeTimeEnd,optional"`    // 下架结束时间
		VendorSongID    string   `json:"vendorSongId,optional"`    // 版权方歌曲ID
		Page            int64    `json:"page,default=1"`
		PageSize        int64    `json:"pageSize,default=10"`
	}

	SumPlayRecord struct {
		// 日期
		LtsTime  string `json:"ltsTime"`
		PlayType int64  `json:"playType"`

		// 歌曲信息
		SongCode     int64  `json:"songCode"`
		SongName     string `json:"songName"`
		SongDuration int64  `json:"songDuration"`
		CpId         int64  `json:"cpId"`
		CpName       string `json:"cpName"`

		// 客户信息
		Vid          int64  `json:"vid"`
		CustomerName string `json:"customerName"`
		UseType      int64  `json:"useType"`

		// 统计数据
		PlayDurationTotal int64 `json:"playDurationTotal"`
		PlayNumTotal      int64 `json:"playNumTotal"`
	}

	// 查询播放记录响应
	SumPlayRecordResp struct {
		Total          int64           `json:"total"`
		List           []SumPlayRecord `json:"list"`
		LastUpdateTime string          `json:"lastUpdateTime"` // 最后更新时间
	}

	SumPlayRecordBySongReq struct {
		LtsTime  string `json:"ltsTime"` // 时间格式：YYYY-MM-DD
		PlayType int    `json:"playType"`
		SongCode int64  `json:"songCode,optional"`
		SongName string `json:"songName,optional"`
		Page     int64  `json:"page,default=1"`
		PageSize int64  `json:"pageSize,default=10"`
	}

	SumPlayRecordByVidReq struct {
		LtsTime      string `json:"ltsTime"` // 时间格式：YYYY-MM-DD
		PlayType     int    `json:"playType"`
		CustomerName string `json:"customerName,optional"`
		UseType      int64  `json:"useType,optional"`
		Vid          int64  `json:"vid,optional"`
		Page         int64  `json:"page,default=1"`
		PageSize     int64  `json:"pageSize,default=10"`
	}

	SumPlayRecordDetail struct {
		// 日期
		LtsTime  string `json:"ltsTime"`
		PlayType int64  `json:"playType"`

		// 歌曲信息
		SongCode     int64  `json:"songCode"`
		SongName     string `json:"songName"`
		SongDuration int64  `json:"songDuration"`
		CpId         int64  `json:"cpId"`
		CpName       string `json:"cpName"`

		// 客户信息
		Vid          int64  `json:"vid"`
		CustomerName string `json:"customerName"`
		UseType      int64  `json:"useType"`

		// 播放信息
		PlayDuration int64  `json:"playDuration"`
		StartTime    string `json:"startTime"`
		EndTime      string `json:"endTime"`
		PlayBackPos  int64  `json:"playBackPos"`
		PlayNum      int64  `json:"playNum"`
		SceneType    int64  `json:"sceneType"`
		RecordType   int64  `json:"recordType"`
		FreeType     int64  `json:"freeType"`
	}

	SumPlayRecordDetailResp struct {
		Total int64                 `json:"total"`
		List  []SumPlayRecordDetail `json:"list"`
	}

	SumPlayRecordDetailBySongReq struct {
		LtsTime      string `json:"ltsTime"` // 时间格式：YYYY-MM-DD
		CustomerName string `json:"customerName,optional"`
		UseType      int64  `json:"useType,optional"`
		SceneType    int64  `json:"sceneType,optional"`
		FreeType     int64  `json:"freeType,optional"`
		PlayType     int64  `json:"playType"`
		Vid          int64  `json:"vid,optional"`
		Page         int64  `json:"page,default=1"`
		PageSize     int64  `json:"pageSize,default=10"`
	}

	SumPlayRecordDetailByVidReq struct {
		LtsTime   string `json:"ltsTime"` // 时间格式：YYYY-MM-DD
		SongName  string `json:"songName,optional"`
		SongCode  int64  `json:"songCode,optional"`
		CpId      int64  `json:"cpId,optional"`
		Vid       int64  `json:"vid,optional"`
		UseType   int64  `json:"useType,optional"`
		SceneType int64  `json:"sceneType,optional"`
		FreeType  int64  `json:"freeType,optional"`
		PlayType  int64  `json:"playType"`
		Page      int64  `json:"page,default=1"`
		PageSize  int64  `json:"pageSize,default=10"`
	}

	PlayRecordSongListReq struct {
		LtsTime  string `json:"ltsTime"` // 上次查询时间
		PlayType int    `json:"playType"`
		Vid      int64  `json:"vid"`
		Page     int64  `json:"page,optional"`
		PageSize int64  `json:"pageSize,optional"`
	}

	DelUniqueIDReq struct {
		UniqueId string `json:"uniqueId"`
	}
)
