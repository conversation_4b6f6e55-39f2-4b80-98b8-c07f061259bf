package ktv_basic

import (
	"time"
)

const (
	LockKeySongDetailExport = "lock_song_detail_export"
)

// 歌曲播放记录表
type SongPlayRecord struct {
	Id           int64     `gorm:"column:id;type:bigint(20);primary_key;AUTO_INCREMENT;comment:记录ID" json:"id"`
	Lts          int64     `gorm:"column:lts;type:bigint(20);comment:时间戳，必须存在;NOT NULL" json:"lts"`
	Vid          int64     `gorm:"column:vid;type:bigint(20);comment:vid信息;NOT NULL" json:"vid"`
	UniqueId     string    `gorm:"column:unique_id;type:varchar(300);comment:唯一ID;NOT NULL" json:"unique_id"`
	RequestIp    string    `gorm:"column:request_ip;type:varchar(100);comment:请求IP（隐私字段，不推送）" json:"request_ip"`
	CpId         int64     `gorm:"column:cp_id;type:bigint(20);comment:版权方ID;NOT NULL" json:"cp_id"`
	SongCode     int64     `gorm:"column:song_code;type:bigint(20);comment:歌曲编号;NOT NULL" json:"song_code"`
	SongDuration int       `gorm:"column:song_duration;type:int(11);default:0;comment:歌曲时长，单位秒;NOT NULL" json:"song_duration"`
	PlayDuration int       `gorm:"column:play_duration;type:int(11);default:0;comment:播放时长，单位秒;NOT NULL" json:"play_duration"`
	StartTime    int64     `gorm:"column:start_time;type:bigint(20);default:0;comment:开始时间，单位秒;NOT NULL" json:"start_time"`
	EndTime      int64     `gorm:"column:end_time;type:bigint(20);default:0;comment:结束时间，单位秒;NOT NULL" json:"end_time"`
	PlaybackPos  int       `gorm:"column:playback_pos;type:int(11);default:0;comment:停播位置，单位秒;NOT NULL" json:"playback_pos"`
	PlayNum      int       `gorm:"column:play_num;type:int(11);default:0;comment:有效次数;NOT NULL" json:"play_num"`
	SceneType    int       `gorm:"column:scene_type;type:int(11);default:0;comment:场景类型;NOT NULL" json:"scene_type"`
	PlayType     int       `gorm:"column:play_type;type:tinyint(4);default:0;comment:播放类型（1:DRM, 2:明文, 3:副歌）;NOT NULL" json:"play_type"`
	FreeType     int       `gorm:"column:free_type;type:tinyint(4);default:0;comment:是否免费（1:免费, 2:不免费）;NOT NULL" json:"free_type"`
	RecordType   int       `gorm:"column:record_type;type:tinyint(4);default:0;comment:记录类型（1:API上报, 2:SDK上报）;NOT NULL" json:"record_type"`
	UseType      int       `gorm:"column:use_type;type:tinyint(4);default:0;comment:用户类型（1:主播, 2:平台）;NOT NULL" json:"use_type"`
	Uid          string    `gorm:"column:uid;type:varchar(100);comment:用户ID（隐私字段，不推送）" json:"uid"`
	ChannelId    string    `gorm:"column:channel_id;type:varchar(100);comment:频道ID（隐私字段，不推送）" json:"channel_id"`
	PushStatus   int       `gorm:"column:push_status;type:tinyint(4);default:0;comment:推送状态（0:未推送, 1:已推送）;NOT NULL" json:"push_status"`
	PushTime     int64     `gorm:"column:push_time;type:bigint(20);comment:推送时间" json:"push_time"`
	CreateTime   time.Time `gorm:"column:create_time;type:timestamp;default:CURRENT_TIMESTAMP;comment:创建时间;NOT NULL" json:"create_time"`
}

func (m *SongPlayRecord) TableName() string {
	return "song_play_record"
}
