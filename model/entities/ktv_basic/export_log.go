package ktv_basic

import (
	"gorm.io/plugin/soft_delete"
)

const (
	ExportLogStateNotStart = 1
	ExportLogStateRunning  = 2
	ExportLogStateDone     = 3
	ExportLogStateCancel   = 4
	ExportLogStateFailed   = 5
)

// 歌曲播放记录表
type ExportLog struct {
	Id        int64   `gorm:"column:id;type:bigint(20);primary_key;AUTO_INCREMENT;comment:记录ID" json:"id"`
	Uuid      string  `gorm:"column:uuid;type:varchar(100);comment:uuid" json:"uuid"`
	UserID    string  `gorm:"column:user_id;type:varchar(100);comment:用户ID" json:"user_id"`
	UserName  string  `gorm:"column:user_name;type:varchar(100);comment:用户名" json:"user_name"`
	Filename  string  `gorm:"column:filename;type:varchar(100);comment:文件名" json:"filename"`
	State     uint8   `gorm:"column:state;type:tinyint(4);default:0;comment:状态（1:未开始, 2:进行中, 3:已完成, 4:已取消, 5:已失败）" json:"state"`
	TotalFile int     `gorm:"column:total_file;type:int(11);comment:总文件数" json:"total_file"`
	DoneTime  string  `gorm:"column:done_time;type:varchar(100);comment:完成时间" json:"done_time"`
	OssUrl    string  `gorm:"column:oss_url;type:varchar(100);comment:OSS地址" json:"oss_url"`
	Schedule  float64 `gorm:"column:schedule;type:decimal(5,2);comment:进度;not null;default:0.00" json:"schedule"`
	CreatedAt int     `gorm:"column:created_at;type:int(11);autoCreateTime" json:"createdAt"`
	UpdatedAt int     `gorm:"column:updated_at;type:int(11);autoCreateTime" json:"updatedAt"`
	DeletedAt soft_delete.DeletedAt
}

func (m *ExportLog) TableName() string {
	return "export_log"
}
