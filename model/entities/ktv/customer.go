package ktv

import (
	"time"
)

// 客户表
type Customer struct {
	Id         int64     `gorm:"column:id;type:int(11) unsigned;primary_key;AUTO_INCREMENT;comment:主键" json:"id"`
	Name       string    `gorm:"column:name;type:varchar(64);comment:名称;NOT NULL" json:"name"`
	Vid        int64     `gorm:"column:vid;type:int(11) unsigned;default:0;comment:客户应用vid;NOT NULL" json:"vid"`
	Cid        int64     `gorm:"column:cid;type:int(11) unsigned;default:0;comment:客户cid;NOT NULL" json:"cid"`
	Ip         string    `gorm:"column:ip;type:text;comment:客户ip白名单，*代表任意ip可访问;NOT NULL" json:"ip"`
	Appid      string    `gorm:"column:appid;type:varchar(128);comment:选填;NOT NULL" json:"appid"`
	Remark     string    `gorm:"column:remark;type:varchar(128);comment:备注;NOT NULL" json:"remark"`
	Status     int8      `gorm:"column:status;type:tinyint(4);default:1;comment:状态 1开启 0关闭;NOT NULL" json:"status"`
	EndTime    int64     `gorm:"column:end_time;type:int(11) unsigned;default:0;comment:过期时间;NOT NULL" json:"end_time"`
	UpdateTime time.Time `gorm:"column:update_time;type:timestamp;default:CURRENT_TIMESTAMP;comment:更新时间;NOT NULL" json:"update_time"`
	CreateTime time.Time `gorm:"column:create_time;type:timestamp;default:CURRENT_TIMESTAMP;comment:创建时间;NOT NULL" json:"create_time"`
}

func (m *Customer) TableName() string {
	return "customer"
}
