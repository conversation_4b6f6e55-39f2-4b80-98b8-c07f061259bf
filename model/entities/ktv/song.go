package ktv

import (
	"time"
)

// 歌曲表
type Song struct {
	Id                uint      `gorm:"column:id;type:int(11) unsigned;primary_key;AUTO_INCREMENT;comment:主键" json:"id"`
	ImportId          uint      `gorm:"column:import_id;type:int(11) unsigned;default:0;comment:导入id，年月日时，如2024121201;NOT NULL" json:"import_id"`
	Type              int       `gorm:"column:type;type:tinyint(4);default:1;comment:歌曲类型：1原唱&伴奏，2伴奏，3原唱，4多音轨;NOT NULL" json:"type"`
	VendorId          uint      `gorm:"column:vendor_id;type:int(11) unsigned;default:0;comment:版权方id（cp的id）;NOT NULL" json:"vendor_id"`
	SongCode          uint64    `gorm:"column:song_code;type:bigint(20) unsigned;default:0;comment:歌曲唯一编号，生成规则：微秒时间戳去除首位的1，末尾后加0;NOT NULL" json:"song_code"`
	Name              string    `gorm:"column:name;type:varchar(64);comment:歌曲名称;NOT NULL" json:"name"`
	Singer            string    `gorm:"column:singer;type:varchar(64);comment:歌手名称;NOT NULL" json:"singer"`
	VendorSongId      string    `gorm:"column:vendor_song_id;type:varchar(64);comment:版权方侧的歌曲id;NOT NULL" json:"vendor_song_id"`
	VendorReleaseTime string    `gorm:"column:vendor_release_time;type:varchar(64);comment:作品发布时间;NOT NULL" json:"vendor_release_time"`
	SongPath          string    `gorm:"column:song_path;type:varchar(255);comment:歌曲存储路径，命名规则（同一首歌不同的码率/副歌片段路径相同，文件名不同）：release/drm/plaintext/$vendor_song_id.mp4;NOT NULL" json:"song_path"`
	DrmPath           string    `gorm:"column:drm_path;type:varchar(255);comment:歌曲drm路径，命名规则： release/drm/$vendor_song_id_1.mp4;NOT NULL" json:"drm_path"`
	LicenseKey        string    `gorm:"column:license_key;type:varchar(255);comment:drm的license密钥，与songcode一一对应;NOT NULL" json:"license_key"`
	PosterPath        string    `gorm:"column:poster_path;type:varchar(255);comment:海报路径，命名规则：pic/release/jpg/3/长_宽/$vendor_song_id.jpg;NOT NULL" json:"poster_path"`
	LyricPath         string    `gorm:"column:lyric_path;type:varchar(255);comment:歌词路径，命名规则（这里{LYRIC}固定不变）：release/lyric/{LYRIC}/1/$vendor_song_id;NOT NULL" json:"lyric_path"`
	LyricType         string    `gorm:"column:lyric_type;type:varchar(64);comment:歌词类型：0 zip格式，1 lrc格式，2 vtt格式。可多个，如"1,2";NOT NULL" json:"lyric_type"`
	PitchType         int       `gorm:"column:pitch_type;type:tinyint(4);default:2;comment:支持打分：1是，2否;NOT NULL" json:"pitch_type"`
	Pitchs            string    `gorm:"column:pitchs;type:varchar(255);comment:打分类型，可多个，如"1,2";NOT NULL" json:"pitchs"`
	HighPart          string    `gorm:"column:high_part;type:varchar(255);comment:副歌片段;NOT NULL" json:"high_part"`
	HighPartType      int       `gorm:"column:high_part_type;type:tinyint(4);default:1;comment:副歌类型，1无副歌，2机器校验，3人工校验;NOT NULL" json:"high_part_type"`
	QualityLevel      string    `gorm:"column:quality_level;type:varchar(255);comment:码率，可多个，如“1,2,3”;NOT NULL" json:"quality_level"`
	TagIds            string    `gorm:"column:tag_ids;type:varchar(255);comment:标签id，可多个，如“1,2,3”;NOT NULL" json:"tag_ids"`
	Duration          uint      `gorm:"column:duration;type:int(11) unsigned;default:0;comment:歌曲时长，秒;NOT NULL" json:"duration"`
	CloseTime         int       `gorm:"column:close_time;type:int(11);default:0;comment:下架时间戳（status设为0时的时间戳+3600s）;NOT NULL" json:"close_time"`
	HotNum            int       `gorm:"column:hot_num;type:int(11);default:0;comment:热度值;NOT NULL" json:"hot_num"`
	Status            int       `gorm:"column:status;type:tinyint(4);default:0;comment:状态：1上架 0下架;NOT NULL" json:"status"`
	UpdateTime        time.Time `gorm:"column:update_time;type:timestamp;default:CURRENT_TIMESTAMP;comment:更新时间;NOT NULL" json:"update_time"`
	CreateTime        time.Time `gorm:"column:create_time;type:timestamp;default:CURRENT_TIMESTAMP;comment:更新时间;NOT NULL" json:"create_time"`
}

func (m *Song) TableName() string {
	return "song"
}
