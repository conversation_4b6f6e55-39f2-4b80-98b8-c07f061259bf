package ck

import (
	"time"
)

type SongPlayRecordAndSong struct {
	ID           uint64    `gorm:"column:id;type:UInt64;primaryKey;comment:ID" json:"id"`
	Lts          int64     `gorm:"column:lts;type:Int64;primaryKey;comment:时间戳，必须存在" json:"lts"`
	Vid          int64     `gorm:"column:vid;type:Int64;comment:vid信息" json:"vid"`
	UniqueID     string    `gorm:"column:unique_id;type:String;comment:唯一ID" json:"unique_id"`
	RequestIP    *string   `gorm:"column:request_ip;type:Nullable(String);comment:请求IP（隐私字段，不推送）" json:"request_ip"`
	CpID         int64     `gorm:"column:cp_id;type:Int64;comment:版权方ID" json:"cp_id"`
	SongCode     int64     `gorm:"column:song_code;type:Int64;comment:歌曲编号" json:"song_code"`
	SongDuration uint32    `gorm:"column:song_duration;type:UInt32;default:0;comment:歌曲时长，单位秒" json:"song_duration"`
	PlayDuration uint32    `gorm:"column:play_duration;type:UInt32;default:0;comment:播放时长，单位秒" json:"play_duration"`
	StartTime    int64     `gorm:"column:start_time;type:Int64;default:0;primaryKey;comment:开始时间，单位秒" json:"start_time"`
	EndTime      int64     `gorm:"column:end_time;type:Int64;default:0;comment:结束时间，单位秒" json:"end_time"`
	PlaybackPos  uint32    `gorm:"column:playback_pos;type:UInt32;default:0;comment:停播位置，单位秒" json:"playback_pos"`
	PlayNum      uint32    `gorm:"column:play_num;type:UInt32;default:0;comment:有效次数" json:"play_num"`
	SceneType    uint32    `gorm:"column:scene_type;type:UInt32;default:0;comment:场景类型" json:"scene_type"`
	PlayType     uint8     `gorm:"column:play_type;type:UInt8;default:0;comment:播放类型（1:DRM, 2:明文, 3:副歌）" json:"play_type"`
	FreeType     uint8     `gorm:"column:free_type;type:UInt8;default:0;comment:是否免费（1:免费, 2:不免费）" json:"free_type"`
	RecordType   uint8     `gorm:"column:record_type;type:UInt8;default:0;comment:记录类型（1:API上报, 2:SDK上报）" json:"record_type"`
	UseType      uint8     `gorm:"column:use_type;type:UInt8;default:0;comment:用户类型（1:主播, 2:平台）" json:"use_type"`
	UID          *string   `gorm:"column:uid;type:Nullable(String);comment:用户ID（隐私字段，不推送）" json:"uid"`
	ChannelID    *string   `gorm:"column:channel_id;type:Nullable(String);comment:频道ID（隐私字段，不推送）" json:"channel_id"`
	PushStatus   uint8     `gorm:"column:push_status;type:UInt8;default:0;comment:推送状态（0:未推送, 1:已推送）" json:"push_status"`
	PushTime     *int64    `gorm:"column:push_time;type:Nullable(Int64);comment:推送时间" json:"push_time"`
	CreateTime   time.Time `gorm:"column:create_time;type:DateTime;default:now();comment:创建时间" json:"create_time"`
	SongName     string    `gorm:"column:song_name;type:String;comment:歌曲名称" json:"song_name"`
	CustomerName string    `gorm:"column:customer_name;type:String;comment:客户名称" json:"customer_name"`
	//CpName       string    `gorm:"column:cp_name;type:String;comment:版权方名称" json:"cp_name"`
}

var RecordTypeMap = map[uint8]string{
	1: "API上报",
	2: "SDK上报",
}

var PlayTypeMap = map[uint8]string{
	1: "DRM",
	2: "明文",
	3: "副歌",
}

var FreeTypeMap = map[uint8]string{
	1: "免费",
	2: "不免费",
}

var UserTypeMap = map[uint8]string{
	1: "主播",
	2: "平台",
}

var SceneTypeMap = map[uint32]string{
	1: "秀场直播K歌",
	2: "秀场直播背景音乐",
	3: "语聊场景K歌",
	4: "语聊场景背景音乐",
	5: "VR场景K歌",
	6: "语聊-合唱（≤20 人）",
	7: "语聊-合唱（＞20 人）",
}

// SongPlayRecord represents the song_play_record table in ClickHouse
type SongPlayRecord struct {
	ID           uint64    `gorm:"column:id;type:UInt64;primaryKey;comment:ID" json:"id"`
	Lts          int64     `gorm:"column:lts;type:Int64;primaryKey;comment:时间戳，必须存在" json:"lts"`
	Vid          int64     `gorm:"column:vid;type:Int64;comment:vid信息" json:"vid"`
	UniqueID     string    `gorm:"column:unique_id;type:String;comment:唯一ID" json:"unique_id"`
	RequestIP    *string   `gorm:"column:request_ip;type:Nullable(String);comment:请求IP（隐私字段，不推送）" json:"request_ip"`
	CpID         int64     `gorm:"column:cp_id;type:Int64;comment:版权方ID" json:"cp_id"`
	SongCode     int64     `gorm:"column:song_code;type:Int64;comment:歌曲编号" json:"song_code"`
	SongDuration uint32    `gorm:"column:song_duration;type:UInt32;default:0;comment:歌曲时长，单位秒" json:"song_duration"`
	PlayDuration uint32    `gorm:"column:play_duration;type:UInt32;default:0;comment:播放时长，单位秒" json:"play_duration"`
	StartTime    int64     `gorm:"column:start_time;type:Int64;default:0;primaryKey;comment:开始时间，单位秒" json:"start_time"`
	EndTime      int64     `gorm:"column:end_time;type:Int64;default:0;comment:结束时间，单位秒" json:"end_time"`
	PlaybackPos  uint32    `gorm:"column:playback_pos;type:UInt32;default:0;comment:停播位置，单位秒" json:"playback_pos"`
	PlayNum      uint32    `gorm:"column:play_num;type:UInt32;default:0;comment:有效次数" json:"play_num"`
	SceneType    uint32    `gorm:"column:scene_type;type:UInt32;default:0;comment:场景类型" json:"scene_type"`
	PlayType     uint8     `gorm:"column:play_type;type:UInt8;default:0;comment:播放类型（1:DRM, 2:明文, 3:副歌）" json:"play_type"`
	FreeType     uint8     `gorm:"column:free_type;type:UInt8;default:0;comment:是否免费（1:免费, 2:不免费）" json:"free_type"`
	RecordType   uint8     `gorm:"column:record_type;type:UInt8;default:0;comment:记录类型（1:API上报, 2:SDK上报）" json:"record_type"`
	UseType      uint8     `gorm:"column:use_type;type:UInt8;default:0;comment:用户类型（1:主播, 2:平台）" json:"use_type"`
	UID          *string   `gorm:"column:uid;type:Nullable(String);comment:用户ID（隐私字段，不推送）" json:"uid"`
	ChannelID    *string   `gorm:"column:channel_id;type:Nullable(String);comment:频道ID（隐私字段，不推送）" json:"channel_id"`
	PushStatus   uint8     `gorm:"column:push_status;type:UInt8;default:0;comment:推送状态（0:未推送, 1:已推送）" json:"push_status"`
	PushTime     *int64    `gorm:"column:push_time;type:Nullable(Int64);comment:推送时间" json:"push_time"`
	CreateTime   time.Time `gorm:"column:create_time;type:DateTime;default:now();comment:创建时间" json:"create_time"`
}

type SongPlayRecordList struct {
	LtsTime           string `gorm:"column:lts_time" json:"lts_time"`
	PlayNumTotal      int64  `gorm:"column:play_num_total" json:"play_num_total"`
	PlayDurationTotal int64  `gorm:"column:play_duration_total" json:"play_duration_total"`
	MaxPlayType       int64  `gorm:"column:max_play_type" json:"max_play_type"`
}

// TableName sets the table name for the model
func (SongPlayRecord) TableName() string {
	return "song_play_record"
}
