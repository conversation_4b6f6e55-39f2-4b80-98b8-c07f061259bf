package ck

import (
	"time"
)

// <PERSON> represents the song table
type Song struct {
	ID                uint32    `gorm:"column:id;type:uint;primaryKey;comment:主键"`
	ImportID          uint32    `gorm:"column:import_id;type:uint;default:0;comment:导入id，年月日时，如2024121201"`
	Type              uint8     `gorm:"column:type;type:tinyint;default:1;comment:歌曲类型：1原唱&伴奏，2伴奏，3原唱，4多音轨"`
	VendorID          uint32    `gorm:"column:vendor_id;type:uint;default:0;comment:版权方id（cp的id）"`
	SongCode          uint64    `gorm:"column:song_code;type:bigint;default:0;comment:歌曲唯一编号，生成规则：微秒时间戳去除首位的1，末尾后加0"`
	Name              string    `gorm:"column:name;type:varchar(64);default:'';comment:歌曲名称"`
	Singer            string    `gorm:"column:singer;type:varchar(64);default:'';comment:歌手名称"`
	VendorSongID      string    `gorm:"column:vendor_song_id;type:varchar(64);default:'';comment:版权方侧的歌曲id"`
	VendorReleaseTime string    `gorm:"column:vendor_release_time;type:varchar(64);default:'';comment:作品发布时间"`
	SongPath          string    `gorm:"column:song_path;type:varchar(255);default:'';comment:歌曲存储路径，命名规则：release/drm/plaintext/$vendor_song_id.mp4"`
	DRMPath           string    `gorm:"column:drm_path;type:varchar(255);default:'';comment:歌曲drm路径，命名规则：release/drm/$vendor_song_id_1.mp4"`
	LicenseKey        string    `gorm:"column:license_key;type:varchar(255);default:'';comment:drm的license密钥，与songcode一一对应"`
	PosterPath        string    `gorm:"column:poster_path;type:varchar(255);default:'';comment:海报路径，命名规则：pic/release/jpg/3/长_宽/$vendor_song_id.jpg"`
	LyricPath         string    `gorm:"column:lyric_path;type:varchar(255);default:'';comment:歌词路径，命名规则：release/lyric/{LYRIC}/1/$vendor_song_id"`
	LyricType         string    `gorm:"column:lyric_type;type:varchar(64);default:'';comment:歌词类型：0 zip格式，1 lrc格式，2 vtt格式。可多个，如'1,2'"`
	PitchType         uint8     `gorm:"column:pitch_type;type:tinyint;default:2;comment:支持打分：1是，2否"`
	Pitchs            string    `gorm:"column:pitchs;type:varchar(255);default:'';comment:打分类型，可多个，如'1,2'"`
	HighPart          string    `gorm:"column:high_part;type:varchar(255);default:'';comment:副歌片段"`
	HighPartType      uint8     `gorm:"column:high_part_type;type:tinyint;default:1;comment:副歌类型，1无副歌，2机器校验，3人工校验"`
	QualityLevel      string    `gorm:"column:quality_level;type:varchar(255);default:'';comment:码率，可多个，如‘1,2,3’"`
	TagIDs            string    `gorm:"column:tag_ids;type:varchar(255);default:'';comment:标签id，可多个，如‘1,2,3’"`
	Duration          uint32    `gorm:"column:duration;type:uint;default:0;comment:歌曲时长，秒"`
	CloseTime         int32     `gorm:"column:close_time;type:int;default:0;comment:下架时间戳（status设为0时的时间戳+3600s）"`
	HotNum            int32     `gorm:"column:hot_num;type:int;default:0;comment:热度值"`
	Status            uint8     `gorm:"column:status;type:tinyint;default:0;comment:状态：1上架 0下架"`
	UpdateTime        time.Time `gorm:"column:update_time;type:datetime;default:current_timestamp;comment:更新时间"`
	CreateTime        time.Time `gorm:"column:create_time;type:datetime;default:current_timestamp;comment:创建时间"`
}

// TableName specifies the table name for GORM
func (Song) TableName() string {
	return "song"
}
