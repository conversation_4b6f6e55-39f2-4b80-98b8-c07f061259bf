FROM testhub.szjixun.cn:9043/public/golang:1.24-alpine AS builder

LABEL stage=gobuilder
#ENV DUBBO_GO_CONFIG_PATH ./conf/dubbogo.yaml
#ENV MODE_ENV test
ENV CGO_ENABLED 0
ENV GOPROXY https://goproxy.cn,direct
#RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
#RUN apk update --no-cache && apk add --no-cache tzdata

WORKDIR /build

COPY ./bin/music-server /app/music-server


FROM testhub.szjixun.cn:9043/public/self-alpine
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk update --no-cache
RUN apk add --no-cache ca-certificates
RUN apk add --no-cache tzdata
COPY ./etc/service_test.yaml /app/etc/service.yaml
COPY ./file /app/etc/file

ENV TZ Asia/Shanghai
WORKDIR /app
COPY --from=builder /app/music-server .
CMD ["/app/music-server"]
