.PHONY: run build test clean docker-build docker-run

# 变量定义
BINARY_NAME=music-server
CONFIG_FILE=etc/api.yaml
BUILD_DIR=bin
DOCKER_IMAGE=music-service:latest

# 默认目标
all: build

# 安装依赖
deps:
	@echo "正在安装依赖..."
	@go mod tidy

# 运行服务
run:
	@echo "正在启动服务..."
	@go run main.go -f $(CONFIG_FILE)

# 编译
build:
	@echo "正在编译..."
	@mkdir -p $(BUILD_DIR)
	@go build -o $(BUILD_DIR)/$(BINARY_NAME) main.go
	@echo "编译完成: $(BUILD_DIR)/$(BINARY_NAME)"

# 运行测试
test:
	@echo "运行测试..."
	@go test -v ./...

# 生成API代码
api:
	@echo "正在生成API代码..."
	@goctl api go -api ./api/auth.api -dir ./internal
	@goctl api go -api ./api/song.api -dir ./internal
	@goctl api go -api ./api/playrecord.api -dir ./internal
	@goctl api go -api ./api/hot.api -dir ./internal
	@goctl api go -api ./api/cp.api -dir ./internal
	@goctl api go -api ./api/customer.api -dir ./internal
	@goctl api go -api ./api/disposition.api -dir ./internal
	@goctl api go -api ./api/user.api -dir ./internal

# 清理编译和临时文件
clean:
	@echo "清理文件..."
	@rm -rf $(BUILD_DIR)
	@rm -f $(BINARY_NAME)
	@go clean

# 构建Docker镜像
docker-build:
	@echo "构建Docker镜像..."
	@docker build -t $(DOCKER_IMAGE) .

# 运行Docker容器
docker-run:
	@echo "运行Docker容器..."
	@docker run -p 8888:8888 --name music-service -d $(DOCKER_IMAGE)

# 帮助信息
help:
	@echo "可用的命令:"
	@echo "  make deps         - 安装项目依赖"
	@echo "  make run          - 运行服务"
	@echo "  make build        - 编译可执行文件"
	@echo "  make test         - 运行测试"
	@echo "  make api          - 生成API代码"
	@echo "  make clean        - 清理编译文件"
	@echo "  make docker-build - 构建Docker镜像"
	@echo "  make docker-run   - 运行Docker容器"
	@echo "  make help         - 显示帮助信息"
