Name: music-api
Host: 0.0.0.0
Port: 8888
Timeout: 360000  # API超时时间，单位毫秒

System:
  Mode: test

Auth:
  AccessSecret: your-secret-key-12345
  AccessExpire: 43200  # 12小时过期

MysqlOut:
  DataSource: aobai_user:asdf1234@tcp(*************:3306)/ktv?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

MysqlIn:
  DataSource: aobai_user:asdf1234@tcp(*************:3306)/ktv-basic?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
#  DataSource: ktv_user:ZAQ!2wsx@tcp(rm-7xvw6sojr7n6781uaxo.mysql.rds.aliyuncs.com:3306)/ktv-basic?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

CkIn:
  DataSource: "clickhouse://default:123456@*************:9000/ktv-basic?dial_timeout=10s&read_timeout=20s"

Log:
  Mode: file
  Path: logs
  Level: info
  Compress: false
  KeepDays: 7

Redis:
   Host: "localhost:6379"  # Redis服务器地址
   Type: node              # Redis类型: node(单节点), cluster(集群)
   Pass: ""                # Redis密码，可选
   DB: 0                   # 数据库编号

File:
  UploadPath: "./file/upload"
  ExportPath: "./file/exports"

Oss:
  Endpoint: "oss-cn-guangzhou.aliyuncs.com"
  AccessKeyID: "LTAI5t7U2Cq9mSiDypLaqjor"
  AccessKeySecret: "******************************"
  BucketName: "ktv-playrecord"
  MonitorFolder: "uploadTest"
  ProcessedFolder: "processedTest"
  FailedFolder: "failedTest"
  CronSpec: "0 */1 * * *"
