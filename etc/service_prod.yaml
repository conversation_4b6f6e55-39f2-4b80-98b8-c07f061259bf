Name: music-api
Host: 0.0.0.0
Port: 8888
Timeout: 360000  # API超时时间，单位毫秒

System:
  Mode: prod

Auth:
  AccessSecret: ktv-user-12345
  AccessExpire: 43200  # 12小时过期

MysqlOut:
  DataSource: ktv_allby_prod:HK#U&%3N8V2HKbwx@tcp(rm-uf693xl5za16l35of3o.mysql.rds.aliyuncs.com:3306)/ktv?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

MysqlIn:
  DataSource: ktv_user:ZAQ!2wsx@tcp(rm-7xvw6sojr7n6781ua.mysql.rds.aliyuncs.com:3306)/ktv-basic?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

CkIn:
  DataSource: "clickhouse://default:123456@*************:9000/ktv-basic?dial_timeout=10s&read_timeout=20s"

Redis:
   Host: "r-7xvqh92mdei5a44y0spd.redis.rds.aliyuncs.com:6379"
   Type: node
   Pass: "ktv_user:ZAQ!2wsx"
   DB: 0

File:
  UploadPath: "./file/upload"
  ExportPath: "./file/exports"

Oss:
  Endpoint: "oss-cn-guangzhou.aliyuncs.com"
  AccessKeyID: "LTAI5t7U2Cq9mSiDypLaqjor"
  AccessKeySecret: "******************************"
  BucketName: "ktv-playrecord"
  MonitorFolder: "upload"
  ProcessedFolder: "processed"
  FailedFolder: "failed"
  CronSpec: "0 14 * * *"

YjxOss:
  EndPoint: "http://oss-cn-beijing.aliyuncs.com"
  AccessKeyId: "LTAI5tRJpJKEkr54ceE5nuYK"
  AccessKeySecret: "******************************"
#  BucketName: "shengwangshuju"
  BucketName: "shengwangshuju-20250617"
  PlatformOssPath: "shengwangshiyongshuju/platform/"
  AnchorOssPath: "shengwangshiyongshuju/host/"
  LicenseOssPath: "shengwangshiyongshuju/drm/"